<w:tbl xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">
    <w:tblPr>
        <w:tblStyle w:val="TableGrid"/>
        <w:tblW w:w="${styling.table.width}" w:type="${styling.table.widthType}"/>
        <w:tblBorders>
            <w:top w:val="${styling.table.borders.top}"/>
            <w:left w:val="${styling.table.borders.left}"/>
            <w:bottom w:val="${styling.table.borders.bottom}"/>
            <w:right w:val="${styling.table.borders.right}"/>
            <w:insideH w:val="${styling.table.borders.insideH}"/>
            <w:insideV w:val="${styling.table.borders.insideV}"/>
        </w:tblBorders>
    </w:tblPr>
    <w:tblGrid>
        <w:gridCol w:w="${styling.columns.benefit.width}"/>
        <#list carriers as carrier>
            <w:gridCol w:w="${styling.columns.carrier.width}"/>
        </#list>
    </w:tblGrid>

    <!-- Header Row -->
    <w:tr>
        <w:trPr>
            <w:trHeight w:val="${styling.rows.header.height}"/>
        </w:trPr>
        <w:tc>
            <w:tcPr>
                <w:tcW w:w="${styling.rows.header.cell.width}" w:type="dxa"/>
                <w:shd w:val="clear" w:color="auto"
                       w:fill="${styling.rows.header.cell.backgroundColor}"/>
                <w:vAlign w:val="${styling.rows.header.cell.verticalAlign}"/>
            </w:tcPr>
            <w:p>
                <w:pPr>
                    <w:jc w:val="${styling.rows.header.text.alignment}"/>
                </w:pPr>
                <w:r>
                    <w:rPr>
                        <w:color w:val="${styling.rows.header.text.color}"/>
                        <w:sz w:val="${styling.rows.header.text.fontSize}"/>
                        <#if styling.rows.header.text.bold><w:b/></#if>
                    </w:rPr>
                    <w:t>Benefit</w:t>
                </w:r>
            </w:p>
        </w:tc>
        <#list carriers as carrier>
            <w:tc>
                <w:tcPr>
                    <w:tcW w:w="${styling.rows.header.cell.width}" w:type="dxa"/>
                    <w:shd w:val="clear" w:color="auto"
                           w:fill="${styling.rows.header.cell.backgroundColor}"/>
                    <w:vAlign w:val="${styling.rows.header.cell.verticalAlign}"/>
                </w:tcPr>
                <w:p>
                    <w:pPr>
                        <w:jc w:val="${styling.rows.header.text.alignment}"/>
                    </w:pPr>
                    <w:r>
                        <w:rPr>
                            <w:color w:val="${styling.rows.header.text.color}"/>
                            <w:sz w:val="${styling.rows.header.text.fontSize}"/>
                            <#if styling.rows.header.text.bold><w:b/></#if>
                        </w:rPr>
                        <w:t>
                            ${carrier}
                        </w:t>
                    </w:r>
                </w:p>
            </w:tc>
        </#list>
    </w:tr>

    <#list sections as section>
        <!-- Section Header Row -->
        <w:tr>
            <w:trPr>
                <w:trHeight w:val="${styling.rows.section.height}"/>
            </w:trPr>
            <w:tc>
                <w:tcPr>
                    <w:tcW w:w="${styling.rows.section.cell.width}" w:type="dxa"/>
                    <w:gridSpan w:val="${carriers?size + 1}"/>
                    <w:shd w:val="clear" w:color="auto"
                           w:fill="${styling.rows.section.cell.backgroundColor}"/>
                    <w:vAlign w:val="${styling.rows.section.cell.verticalAlign}"/>
                </w:tcPr>
                <w:p>
                    <w:pPr>
                        <w:jc w:val="${styling.rows.section.text.alignment}"/>
                    </w:pPr>
                    <w:r>
                        <w:rPr>
                            <w:color w:val="${styling.rows.section.text.color}"/>
                            <w:sz w:val="${styling.rows.section.text.fontSize}"/>
                            <#if styling.rows.section.text.bold><w:b/></#if>
                        </w:rPr>
                        <w:t>
                            ${section.name}
                        </w:t>
                    </w:r>
                </w:p>
            </w:tc>
        </w:tr>

        <#list section.benefits as benefit>
            <!-- Benefit Row -->
            <w:tr>
                <w:trPr>
                    <w:trHeight w:val="${styling.rows.benefit.height}"/>
                </w:trPr>
                <w:tc>
                    <w:tcPr>
                        <w:tcW w:w="${styling.rows.benefit.benefitCell.width}" w:type="dxa"/>
                        <w:vAlign w:val="${styling.rows.benefit.benefitCell.verticalAlign}"/>
                    </w:tcPr>
                    <w:p>
                        <w:pPr>
                            <w:jc w:val="${styling.rows.benefit.benefitText.alignment}"/>
                        </w:pPr>
                        <w:r>
                            <w:rPr>
                                <w:color w:val="${styling.rows.benefit.benefitText.color}"/>
                                <w:sz w:val="${styling.rows.benefit.benefitText.fontSize}"/>
                                <#if styling.rows.benefit.benefitText.bold><w:b/></#if>
                            </w:rPr>
                            <w:t>
                                ${benefit.name}
                            </w:t>
                        </w:r>
                    </w:p>
                </w:tc>
                <#list carriers as carrier>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="${styling.rows.benefit.carrierCell.width}" w:type="dxa"/>
                            <w:vAlign w:val="${styling.rows.benefit.carrierCell.verticalAlign}"/>
                        </w:tcPr>
                        <w:p>
                            <w:pPr>
                                <w:jc w:val="${styling.rows.benefit.carrierText.alignment}"/>
                            </w:pPr>
                            <w:r>
                                <w:rPr>
                                    <w:color w:val="${styling.rows.benefit.carrierText.color}"/>
                                    <w:sz w:val="${styling.rows.benefit.carrierText.fontSize}"/>
                                    <#if styling.rows.benefit.carrierText.bold><w:b/></#if>
                                </w:rPr>
                                <w:t>
                                    ${benefit.values[carrier]}
                                </w:t>
                            </w:r>
                        </w:p>
                    </w:tc>
                </#list>
            </w:tr>
        </#list>
    </#list>
</w:tbl>