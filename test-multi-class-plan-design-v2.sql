-- Test script for the updated multi-class plan design function
-- This tests the new logic that appends employee class names to section IDs and names

-- First, check what employee classes exist for your plan
SELECT 
    p.plan_uuid,
    COUNT(DISTINCT ec.name) as total_employee_classes,
    array_agg(DISTINCT ec.name ORDER BY ec.name) as employee_class_names
FROM sandf.plan p
JOIN sandf.quote q ON q.plan_id = p.plan_id
JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
WHERE p.plan_uuid = 'your-plan-uuid-here'::uuid
AND ecq.formatted_quote_details IS NOT NULL
AND ecq.formatted_quote_details != '{}'::jsonb
GROUP BY p.plan_uuid;

-- Test the updated function with multi-class plan
-- Replace 'your-plan-uuid-here' with an actual plan UUID that has multiple employee classes
SELECT sandf.fn_get_plan_design_report_v2(
    'your-plan-uuid-here'::uuid,
    1, -- user_id
    NULL, -- includes (null means include all)
    NULL  -- excludes (null means exclude none)
);

-- Expected output structure for multi-class:
-- [
--   {
--     "carriers": ["Carrier1", "Carrier2", ...],
--     "sections": [
--       {
--         "id": "lifeinsuranceadad-1classname",
--         "name": "Life Insurance & AD&D - 1classname",
--         "benefits": [...]
--       },
--       {
--         "id": "lifeinsuranceadad-2classname", 
--         "name": "Life Insurance & AD&D - 2classname",
--         "benefits": [...]
--       },
--       {
--         "id": "dependentlife-1classname",
--         "name": "Dependent Life - 1classname", 
--         "benefits": [...]
--       },
--       {
--         "id": "dependentlife-2classname",
--         "name": "Dependent Life - 2classname",
--         "benefits": [...]
--       }
--     ]
--   }
-- ]

-- Test with single class plan (should use original logic)
-- Replace with a plan UUID that has only one employee class
SELECT sandf.fn_get_plan_design_report_v2(
    'single-class-plan-uuid-here'::uuid,
    1, -- user_id
    NULL, -- includes
    NULL  -- excludes
);

-- Expected output for single class should remain the same as before
