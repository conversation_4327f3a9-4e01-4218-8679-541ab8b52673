# Rate Sheet v2 - Multi-Class Support with Pagination

## Overview

The `fn_get_rate_sheet_v2` function is a new implementation that supports both single-class (RTQ) and multi-class scenarios with pagination. It follows the same pattern as `plan-design-v2.sql` but is specifically designed for rate sheet calculations with a limit of 16 rows per page.

## Key Features

### 1. Multi-Class Support
- **Dynamic Employee Class Detection**: Automatically detects all employee classes for a plan
- **Class-Specific Processing**: Processes premiums for each employee class separately
- **Employee Class Suffixes**: Appends employee class names to calculation IDs and names

### 2. Pagination
- **16 Rows Per Page**: Each page contains a maximum of 16 calculation rows
- **Array of Objects**: Returns an array of page objects for easy frontend consumption
- **Consistent Structure**: Each page has the same structure with `carriers` and `calculations` arrays

### 3. Global Employee Class Resolution
- **Uses Global Resolver**: Leverages `sandf.fn_get_resolved_employee_class()` for consistent employee class handling
- **Backward Compatible**: Works with existing single-class (RTQ) plans
- **Future-Proof**: Automatically adapts to multi-class plans

## Function Signature

```sql
sandf.fn_get_rate_sheet_v2(
    plan_uuid_param TEXT,
    user_id_param TEXT DEFAULT NULL
)
RETURNS JSONB
```

## Return Structure

### Single Page (≤16 rows)
```json
[
  {
    "carriers": ["Carrier A", "Carrier B", "Carrier C"],
    "calculations": [
      {
        "carrier": "Carrier A",
        "employeeClass": "RTQ",
        "monthlyPremium": 1500.00,
        "annualPremium": 18000.00,
        "calculationId": "carriera-rtq",
        "calculationName": "Carrier A - RTQ"
      }
    ]
  }
]
```

### Multiple Pages (>16 rows)
```json
[
  {
    "carriers": ["Carrier A", "Carrier B", "Carrier C"],
    "calculations": [/* 16 calculations */]
  },
  {
    "carriers": ["Carrier A", "Carrier B", "Carrier C"],
    "calculations": [/* remaining calculations */]
  }
]
```

## Multi-Class Behavior

### Employee Class Processing
1. **Detection**: Automatically detects all employee classes in the plan
2. **Iteration**: Processes each employee class separately
3. **Naming**: Appends employee class name to calculation identifiers

### Example Multi-Class Output
For a plan with classes "Class 1 - Salaried EE's" and "Class 2 - Hourly EE's":

```json
[
  {
    "carriers": ["Carrier A", "Carrier B"],
    "calculations": [
      {
        "carrier": "Carrier A",
        "employeeClass": "Class 1 - Salaried EE's",
        "calculationId": "carriera-class1-salariedees",
        "calculationName": "Carrier A - Class 1 - Salaried EE's"
      },
      {
        "carrier": "Carrier A",
        "employeeClass": "Class 2 - Hourly EE's",
        "calculationId": "carriera-class2-hourlyees",
        "calculationName": "Carrier A - Class 2 - Hourly EE's"
      }
    ]
  }
]
```

## Premium Calculation Logic

### Nested Premium Structure
Handles complex premium structures like Extended Health:
```json
{
  "single": {"premium": "100.00"},
  "couple": {"premium": "200.00"},
  "family": {"premium": "300.00"}
}
```

### Direct Premium Structure
Handles simple premium structures:
```json
{
  "premium": "150.00"
}
```

### Calculation Process
1. **Initialize Totals**: Start with zero monthly and annual premiums
2. **Process Benefits**: Iterate through all benefit premiums
3. **Handle Structure**: Detect nested vs. direct premium structure
4. **Sum Premiums**: Add all premiums for monthly total
5. **Calculate Annual**: Multiply monthly by 12 for annual total

## Deployment

### Files Created
1. `multi-class/rate-sheet-v2.sql` - Main function implementation
2. `test-rate-sheet-v2.sql` - Comprehensive test script
3. `deploy-rate-sheet-v2.sql` - Deployment script
4. `rate-sheet-v2-summary.md` - This documentation

### Deployment Steps
1. Execute the deployment script: `\i deploy-rate-sheet-v2.sql`
2. Verify function creation
3. Test with your plan UUID using the test script
4. Grant permissions if needed

## Testing

Use the provided test script to verify functionality:

```sql
-- Basic test
SELECT jsonb_pretty(
    sandf.fn_get_rate_sheet_v2(
        'your-plan-uuid-here',
        'your-user-id'
    )
);

-- Pagination analysis
WITH rate_sheet_data AS (
    SELECT sandf.fn_get_rate_sheet_v2('your-plan-uuid', 'your-user-id') as result
),
pages AS (
    SELECT 
        generate_subscripts(ARRAY(SELECT jsonb_array_elements(result)), 1) as page_number,
        jsonb_array_elements(result) as page_data
    FROM rate_sheet_data
)
SELECT 
    page_number,
    jsonb_array_length(page_data -> 'calculations') as calculations_count
FROM pages;
```

## Integration

### Frontend Integration
The paginated structure makes it easy for frontend applications to:
- Display rate sheets page by page
- Show consistent carrier ordering across pages
- Handle both single and multi-class scenarios seamlessly

### Backend Integration
The function integrates with existing systems by:
- Using the same parameter structure as the original function
- Maintaining consistent data types and formats
- Leveraging existing global employee class resolution

## Performance Considerations

- **Efficient Queries**: Uses optimized queries with proper ordering
- **Memory Management**: Processes data in chunks for large datasets
- **Pagination**: Reduces memory usage for plans with many calculations
- **Indexing**: Leverages existing database indexes for optimal performance
