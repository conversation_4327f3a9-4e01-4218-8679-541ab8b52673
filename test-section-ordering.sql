-- Test script to verify section ordering in multi-class scenarios

-- Step 1: Check the section ordering for a multi-class plan
-- Replace 'your-plan-uuid-here' with your actual plan UUID
SELECT 
    'Section Ordering Test' as test_name,
    page_index,
    section_index,
    section_data ->> 'id' as section_id,
    section_data ->> 'name' as section_name,
    jsonb_array_length(section_data -> 'benefits') as benefit_count,
    -- Extract original section name (without class suffix)
    regexp_replace(section_data ->> 'name', ' - [0-9]+[A-Za-z]+$', '') as original_section_name,
    -- Extract class suffix
    regexp_replace(section_data ->> 'name', '^.* - ([0-9]+[A-Za-z]+)$', '\1') as class_suffix
FROM (
    SELECT 
        generate_subscripts(result_array, 1) as page_index,
        result_array[generate_subscripts(result_array, 1)] as page_data
    FROM (
        SELECT 
            ARRAY(SELECT jsonb_array_elements(
                sandf.fn_get_plan_design_report_multi_v2(
                    'your-plan-uuid-here',  -- Replace with your actual plan UUID
                    'your-user-id',         -- Replace with your user ID
                    NULL,
                    NULL
                )::jsonb
            )) as result_array
    ) sub
) pages,
LATERAL (
    SELECT 
        generate_subscripts(sections_array, 1) as section_index,
        sections_array[generate_subscripts(sections_array, 1)] as section_data
    FROM (
        SELECT ARRAY(SELECT jsonb_array_elements(page_data -> 'sections')) as sections_array
    ) sections
) section_details
ORDER BY page_index, section_index;

-- Step 2: Verify that sections are grouped by original section name, then by class
SELECT 
    'Section Grouping Verification' as test_name,
    original_section_name,
    class_suffix,
    section_name,
    section_id
FROM (
    SELECT 
        section_data ->> 'id' as section_id,
        section_data ->> 'name' as section_name,
        -- Extract original section name (without class suffix)
        regexp_replace(section_data ->> 'name', ' - [0-9]+[A-Za-z]+$', '') as original_section_name,
        -- Extract class suffix
        regexp_replace(section_data ->> 'name', '^.* - ([0-9]+[A-Za-z]+)$', '\1') as class_suffix,
        ROW_NUMBER() OVER (ORDER BY 
            -- This should match the ordering logic in the function
            regexp_replace(section_data ->> 'name', ' - [0-9]+[A-Za-z]+$', ''),
            regexp_replace(section_data ->> 'name', '^.* - ([0-9]+[A-Za-z]+)$', '\1')
        ) as order_rank
    FROM (
        SELECT 
            generate_subscripts(result_array, 1) as page_index,
            result_array[generate_subscripts(result_array, 1)] as page_data
        FROM (
            SELECT 
                ARRAY(SELECT jsonb_array_elements(
                    sandf.fn_get_plan_design_report_multi_v2(
                        'your-plan-uuid-here',
                        'your-user-id',
                        NULL,
                        NULL
                    )::jsonb
                )) as result_array
        ) sub
    ) pages,
    LATERAL (
        SELECT 
            generate_subscripts(sections_array, 1) as section_index,
            sections_array[generate_subscripts(sections_array, 1)] as section_data
        FROM (
            SELECT ARRAY(SELECT jsonb_array_elements(page_data -> 'sections')) as sections_array
        ) sections
    ) section_details
) ordered_sections
ORDER BY order_rank;

-- Step 3: Compare with single-class ordering to ensure consistency
SELECT 
    'Single vs Multi-Class Comparison' as test_name,
    'Single-Class' as plan_type,
    section_data ->> 'id' as section_id,
    section_data ->> 'name' as section_name
FROM (
    SELECT 
        ARRAY(SELECT jsonb_array_elements(
            sandf.fn_get_plan_design_report(
                'single-class-plan-uuid-here',  -- Replace with single-class plan UUID
                'your-user-id',
                NULL,
                NULL
            )::jsonb
        )) as result_array
) single_class,
LATERAL (
    SELECT 
        generate_subscripts(sections_array, 1) as section_index,
        sections_array[generate_subscripts(sections_array, 1)] as section_data
    FROM (
        SELECT ARRAY(SELECT jsonb_array_elements(result_array[1] -> 'sections')) as sections_array
    ) sections
) section_details

UNION ALL

SELECT 
    'Single vs Multi-Class Comparison' as test_name,
    'Multi-Class' as plan_type,
    section_data ->> 'id' as section_id,
    section_data ->> 'name' as section_name
FROM (
    SELECT 
        ARRAY(SELECT jsonb_array_elements(
            sandf.fn_get_plan_design_report_multi_v2(
                'your-plan-uuid-here',
                'your-user-id',
                NULL,
                NULL
            )::jsonb
        )) as result_array
) multi_class,
LATERAL (
    SELECT 
        generate_subscripts(sections_array, 1) as section_index,
        sections_array[generate_subscripts(sections_array, 1)] as section_data
    FROM (
        SELECT ARRAY(SELECT jsonb_array_elements(result_array[1] -> 'sections')) as sections_array
    ) sections
) section_details
ORDER BY plan_type, section_id;

-- Expected Results for Multi-Class:
-- Sections should be ordered like:
-- 1. Life Insurance & AD&D - 1RTQ
-- 2. Life Insurance & AD&D - 2Management
-- 3. Dependent Life - 1RTQ  
-- 4. Dependent Life - 2Management
-- 5. Dental - 1RTQ
-- 6. Dental - 2Management
-- etc.

-- NOT like:
-- 1. Dental - 1RTQ
-- 2. Dental - 2Management
-- 3. Dependent Life - 1RTQ
-- 4. Dependent Life - 2Management
-- 5. Life Insurance & AD&D - 1RTQ
-- 6. Life Insurance & AD&D - 2Management
