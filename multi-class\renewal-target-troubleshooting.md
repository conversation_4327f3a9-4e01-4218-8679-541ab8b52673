# Renewal Target Function Troubleshooting Guide

## Issue: Empty Results (carriers: [], sections: [])

### Possible Causes and Solutions

#### 1. **No Employee Classes Found**
**Symptoms:** Debug shows `employee_class_count = 0`

**Check:**
```sql
SELECT COUNT(DISTINCT ec.name) as class_count, array_agg(DISTINCT ec.name) as classes
FROM sandf.plan p
JOIN sandf.quote q ON q.plan_id = p.plan_id
JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
WHERE p.plan_uuid = 'your-plan-uuid'
AND ecq.formatted_quote_details IS NOT NULL
AND ecq.formatted_quote_details != '{}'::jsonb;
```

**Solution:** Ensure the plan has employee classes with formatted quote details.

#### 2. **No Formatted Quote Details**
**Symptoms:** Debug shows `quote_count = 0` for formatted details

**Check:**
```sql
SELECT 
    ecq.quote_id,
    ecq.formatted_quote_details IS NOT NULL as has_details,
    ecq.formatted_quote_details != '{}'::jsonb as not_empty
FROM sandf.plan p
JOIN sandf.quote q ON q.plan_id = p.plan_id
JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
WHERE p.plan_uuid = 'your-plan-uuid';
```

**Solution:** Ensure quotes have properly formatted details.

#### 3. **Missing Premium Data Structure**
**Symptoms:** Function finds classes but no premium calculations

**Check:**
```sql
SELECT 
    ec.name,
    c.description,
    ecq.formatted_quote_details #> '{benefitPremiums}' IS NOT NULL as has_benefit_premiums,
    ecq.formatted_quote_details #> '{benefitPremiums,extendedHealthPremium}' IS NOT NULL as has_ehc,
    ecq.formatted_quote_details #> '{benefitPremiums,dentalCarePremium}' IS NOT NULL as has_dental,
    ecq.formatted_quote_details #> '{benefitPremiums,ratingFactors}' IS NOT NULL as has_rating_factors
FROM sandf.plan p
JOIN sandf.quote q ON q.plan_id = p.plan_id
JOIN sandf.carrier c ON q.carrier_id = c.carrier_id
JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
WHERE p.plan_uuid = 'your-plan-uuid';
```

**Solution:** Verify the JSON structure contains the expected premium and rating factor data.

#### 4. **Wrong Data Structure Format**
**Symptoms:** Function processes but calculates zero values

**Check Premium Structure:**
```sql
SELECT 
    ecq.formatted_quote_details #> '{benefitPremiums,extendedHealthPremium}' as ehc_structure,
    CASE 
        WHEN ecq.formatted_quote_details #> '{benefitPremiums,extendedHealthPremium}' ? 'single' THEN 'nested'
        ELSE 'direct'
    END as structure_type
FROM sandf.plan p
JOIN sandf.quote q ON q.plan_id = p.plan_id
JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
WHERE p.plan_uuid = 'your-plan-uuid'
LIMIT 1;
```

#### 5. **User Preference Function Issues**
**Symptoms:** Carriers found but not ordered properly

**Check:**
```sql
SELECT sandf.get_user_preference_order(
    'your-user-id',
    'your-plan-uuid',
    quote_id,
    quote_uuid,
    999999
) as preference_order
FROM sandf.quote q
JOIN sandf.plan p ON q.plan_id = p.plan_id
WHERE p.plan_uuid = 'your-plan-uuid'
LIMIT 3;
```

### Quick Fixes

#### Fix 1: Test with Original Function Pattern
If the multi-class version isn't working, test with a simplified single-class approach:

```sql
-- Use the old global resolver approach temporarily
SELECT sandf.fn_get_resolved_employee_class('your-plan-uuid');
```

#### Fix 2: Check Function Dependencies
Ensure required functions exist:

```sql
SELECT routine_name 
FROM information_schema.routines 
WHERE routine_schema = 'sandf' 
AND routine_name IN ('get_user_preference_order', 'safe_parse_numeric', 'fn_get_resolved_employee_class');
```

#### Fix 3: Verify Data Types
Check if plan_uuid is being passed correctly:

```sql
-- Test direct plan lookup
SELECT plan_id FROM sandf.plan WHERE plan_uuid = 'your-plan-uuid'::uuid;
```

### Debug Output Analysis

When running the debug function, look for these patterns:

1. **"Plan not found!"** → Check UUID format and existence
2. **"Total quotes for plan: 0"** → No quotes exist for this plan
3. **"Quotes with formatted details: 0"** → Quotes exist but no formatted details
4. **"Found 0 employee classes"** → Employee class data missing or malformed
5. **"Found 0 carriers"** → Carrier data missing or not linked properly

### Expected Data Structure

The function expects this JSON structure:
```json
{
  "benefitPremiums": {
    "extendedHealthPremium": {
      "single": {"volume": "10", "premium": "100.00"},
      "couple": {"volume": "5", "premium": "200.00"},
      "family": {"volume": "3", "premium": "300.00"}
    },
    "dentalCarePremium": {
      "single": {"volume": "10", "premium": "50.00"},
      "couple": {"volume": "5", "premium": "100.00"},
      "family": {"volume": "3", "premium": "150.00"}
    },
    "ratingFactors": {
      "extendedHealthRatingFactors": {
        "pooling": "5.0",
        "ibnr": "2.0",
        "targetLossRatio": "85.0"
      },
      "dentalRatingFactors": {
        "ibnr": "1.0",
        "targetLossRatio": "80.0"
      }
    }
  }
}
```
