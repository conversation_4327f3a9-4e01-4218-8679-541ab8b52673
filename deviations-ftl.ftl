<#-- Deviations Table Template with carrierData structure -->
<w:tbl xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">
    <w:tblPr>
        <w:tblStyle w:val="TableGrid"/>
        <w:tblW w:w="${styling.table.width}" w:type="${styling.table.widthType}"/>
        <w:tblBorders>
            <w:top w:val="${styling.table.borders.top}"/>
            <w:left w:val="${styling.table.borders.left}"/>
            <w:bottom w:val="${styling.table.borders.bottom}"/>
            <w:right w:val="${styling.table.borders.right}"/>
            <w:insideH w:val="${styling.table.borders.insideH}"/>
            <w:insideV w:val="${styling.table.borders.insideV}"/>
        </w:tblBorders>
    </w:tblPr>
    <w:tblGrid>
        <#-- Create 2 columns for the table -->
        <w:gridCol w:w="${styling.columns.carrier.width}"/>
        <w:gridCol w:w="${styling.columns.carrier.width}"/>
    </w:tblGrid>

    <!-- Header Row with "Deviations" title -->
    <w:tr>
        <w:trPr>
            <w:trHeight w:val="${styling.rows.header.height}"/>
        </w:trPr>
        <w:tc>
            <w:tcPr>
                <w:tcW w:w="${styling.rows.header.cell.width}" w:type="dxa"/>
                <w:gridSpan w:val="2"/>
                <w:shd w:val="clear" w:color="auto"
                       w:fill="${styling.rows.section.cell.backgroundColor}"/>
                <w:vAlign w:val="${styling.rows.header.cell.verticalAlign}"/>
            </w:tcPr>
            <w:p>
                <w:pPr>
                    <w:jc w:val="${styling.rows.header.text.alignment}"/>
                </w:pPr>
                <w:r>
                    <w:rPr>
                        <w:color w:val="${styling.rows.header.text.color}"/>
                        <w:sz w:val="${styling.rows.header.text.fontSize}"/>
                        <#if styling.rows.header.text.bold><w:b/></#if>
                    </w:rPr>
                    <w:t>Deviations</w:t>
                </w:r>
            </w:p>
        </w:tc>
    </w:tr>


    <#-- Process carrierData in pairs to fill 2x2 table structure -->
    <#list 0..<(carrierData?size/2) as rowIndex>
        <w:tr>
            <w:trPr>
                <w:trHeight w:val="${styling.rows.benefit.height}"/>
            </w:trPr>

            <#-- First cell (left column) -->
            <#assign leftIndex = rowIndex * 2>
            <#if (carrierData[leftIndex])??>
                <#assign leftItem = carrierData[leftIndex]>
                <w:tc>
                    <w:tcPr>
                        <w:tcW w:w="${styling.rows.benefit.carrierCell.width}" w:type="dxa"/>
                        <w:vAlign w:val="${styling.rows.benefit.carrierCell.verticalAlign}"/>
                        <w:tcMar>
                            <w:top w:w="80" w:type="dxa"/>
                            <w:bottom w:w="80" w:type="dxa"/>
                        </w:tcMar>
                    </w:tcPr>
                    <w:p>
                        <w:pPr>
                            <w:jc w:val="${styling.rows.benefit.carrierText.alignment}"/>
                        </w:pPr>
                        <#-- Add carrier name as bold prefix -->
                        <w:r>
                            <w:rPr>
                                <w:color w:val="${styling.rows.benefit.carrierText.color}"/>
                                <w:sz w:val="${styling.rows.benefit.carrierText.fontSize}"/>
                                <w:b/>
                            </w:rPr>
                            <w:t>${leftItem.name}: </w:t>
                        </w:r>
                        <#-- Add deviation text -->
                        <w:r>
                            <w:rPr>
                                <w:color w:val="${styling.rows.benefit.carrierText.color}"/>
                                <w:sz w:val="${styling.rows.benefit.carrierText.fontSize}"/>
                                <#if styling.rows.benefit.carrierText.bold><w:b/></#if>
                            </w:rPr>
                            <w:t xml:space="preserve">${leftItem.deviations?replace('\n', '\n ')}</w:t>
                        </w:r>
                    </w:p>
                </w:tc>
            <#else>
                <#-- Empty cell if no data -->
                <w:tc>
                    <w:tcPr>
                        <w:tcW w:w="${styling.rows.benefit.carrierCell.width}" w:type="dxa"/>
                        <w:vAlign w:val="${styling.rows.benefit.carrierCell.verticalAlign}"/>
                    </w:tcPr>
                    <w:p>
                        <w:r>
                            <w:rPr>
                                <w:color w:val="${styling.rows.benefit.carrierText.color}"/>
                                <w:sz w:val="${styling.rows.benefit.carrierText.fontSize}"/>
                            </w:rPr>
                            <w:t>-</w:t>
                        </w:r>
                    </w:p>
                </w:tc>
            </#if>

            <#-- Second cell (right column) -->
            <#assign rightIndex = rowIndex * 2 + 1>
            <#if (carrierData[rightIndex])??>
                <#assign rightItem = carrierData[rightIndex]>
                <w:tc>
                    <w:tcPr>
                        <w:tcW w:w="${styling.rows.benefit.carrierCell.width}" w:type="dxa"/>
                        <w:vAlign w:val="${styling.rows.benefit.carrierCell.verticalAlign}"/>
                        <w:tcMar>
                            <w:top w:w="80" w:type="dxa"/>
                            <w:bottom w:w="80" w:type="dxa"/>
                        </w:tcMar>
                    </w:tcPr>
                    <w:p>
                        <w:pPr>
                            <w:jc w:val="${styling.rows.benefit.carrierText.alignment}"/>
                        </w:pPr>
                        <#-- Add carrier name as bold prefix -->
                        <w:r>
                            <w:rPr>
                                <w:color w:val="${styling.rows.benefit.carrierText.color}"/>
                                <w:sz w:val="${styling.rows.benefit.carrierText.fontSize}"/>
                                <w:b/>
                            </w:rPr>
                            <w:t>${rightItem.name}: </w:t>
                        </w:r>
                        <#-- Add deviation text -->
                        <w:r>
                            <w:rPr>
                                <w:color w:val="${styling.rows.benefit.carrierText.color}"/>
                                <w:sz w:val="${styling.rows.benefit.carrierText.fontSize}"/>
                                <#if styling.rows.benefit.carrierText.bold><w:b/></#if>
                            </w:rPr>
                            <w:t xml:space="preserve">${rightItem.deviations?replace('\n', '\n ')}</w:t>
                        </w:r>
                    </w:p>
                </w:tc>
            <#else>
                <#-- Empty cell if no data -->
                <w:tc>
                    <w:tcPr>
                        <w:tcW w:w="${styling.rows.benefit.carrierCell.width}" w:type="dxa"/>
                        <w:vAlign w:val="${styling.rows.benefit.carrierCell.verticalAlign}"/>
                    </w:tcPr>
                    <w:p>
                        <w:r>
                            <w:rPr>
                                <w:color w:val="${styling.rows.benefit.carrierText.color}"/>
                                <w:sz w:val="${styling.rows.benefit.carrierText.fontSize}"/>
                            </w:rPr>
                            <w:t>-</w:t>
                        </w:r>
                    </w:p>
                </w:tc>
            </#if>
        </w:tr>
    </#list>
</w:tbl>