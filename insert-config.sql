[
  {
    "carriers": [
      "Chambers Plan",
      "Manulife",
      "Sunlife",
      "Victor"
    ],
    "sections": [
      {
        "id": "lifeinsuranceadad-1classname",
        "name": "Life Insurance & AD&D - 1classname",
        "benefits": [
          {
            "key": "coverageLife",
            "name": "Amount of Coverage",
            "values": {
              "Victor": "2 times annual earnings",
              "Sunlife": "2 times annual earnings",
              "Manulife": "2 times annual earnings",
              "Chambers Plan": "2 times annual earnings"
            },
            "section": "lifeInsuranceADAD1classname"
          },
          {
            "key": "ageReduction",
            "name": "Age Reduction",
            "values": {
              "Victor": "50% at age 65",
              "Sunlife": "50% at age 65, 50% at age 70",
              "Manulife": "50% at age 65",
              "Chambers Plan": "50% at age 65"
            },
            "section": "lifeInsuranceADAD1classname"
          },
          {
            "key": "terminationAgeLife",
            "name": "Termination Age - Life",
            "values": {
              "Victor": "Retirement or to age 80",
              "Sunlife": "Retirement or to age 75",
              "Manulife": "Retirement or to age 75",
              "Chambers Plan": "Retirement or to age 75"
            },
            "section": "lifeInsuranceADAD1classname"
          },
          {
            "key": "terminationAgeADAD",
            "name": "Termination Age - AD&D",
            "values": {
              "Victor": "Retirement or to age 80",
              "Sunlife": "Retirement or to age 75",
              "Manulife": "Retirement or to age 71",
              "Chambers Plan": "Retirement or to age 75"
            },
            "section": "lifeInsuranceADAD1classname"
          },
          {
            "key": "maximumLife",
            "name": "Maximum - Life",
            "values": {
              "Victor": "$300,000",
              "Sunlife": "$300,000",
              "Manulife": "$300,000",
              "Chambers Plan": "$300,000"
            },
            "section": "lifeInsuranceADAD1classname"
          },
          {
            "key": "maximumADAD",
            "name": "Maximum - AD&D",
            "values": {
              "Victor": "$300,000",
              "Sunlife": "$300,000",
              "Manulife": "$300,000",
              "Chambers Plan": "$300,000"
            },
            "section": "lifeInsuranceADAD1classname"
          }
        ]
      },
       {
        "id": "lifeinsuranceadad-2classname",
        "name": "Life Insurance & AD&D - 2classname",
        "benefits": [
          {
            "key": "coverageLife",
            "name": "Amount of Coverage",
            "values": {
              "Victor": "2 times annual earnings",
              "Sunlife": "2 times annual earnings",
              "Manulife": "2 times annual earnings",
              "Chambers Plan": "2 times annual earnings"
            },
            "section": "lifeInsuranceADAD2classname"
          },
          {
            "key": "ageReduction",
            "name": "Age Reduction",
            "values": {
              "Victor": "50% at age 65",
              "Sunlife": "50% at age 65, 50% at age 70",
              "Manulife": "50% at age 65",
              "Chambers Plan": "50% at age 65"
            },
            "section": "lifeInsuranceADAD2classname"
          },
          {
            "key": "terminationAgeLife",
            "name": "Termination Age - Life",
            "values": {
              "Victor": "Retirement or to age 80",
              "Sunlife": "Retirement or to age 75",
              "Manulife": "Retirement or to age 75",
              "Chambers Plan": "Retirement or to age 75"
            },
            "section": "lifeInsuranceADAD2classname"
          },
          {
            "key": "terminationAgeADAD",
            "name": "Termination Age - AD&D",
            "values": {
              "Victor": "Retirement or to age 80",
              "Sunlife": "Retirement or to age 75",
              "Manulife": "Retirement or to age 71",
              "Chambers Plan": "Retirement or to age 75"
            },
            "section": "lifeInsuranceADAD2classname"
          },
          {
            "key": "maximumLife",
            "name": "Maximum - Life",
            "values": {
              "Victor": "$300,000",
              "Sunlife": "$300,000",
              "Manulife": "$300,000",
              "Chambers Plan": "$300,000"
            },
            "section": "lifeInsuranceADAD2classname"
          },
          {
            "key": "maximumADAD",
            "name": "Maximum - AD&D",
            "values": {
              "Victor": "$300,000",
              "Sunlife": "$300,000",
              "Manulife": "$300,000",
              "Chambers Plan": "$300,000"
            },
            "section": "lifeInsuranceADAD2classname"
          }
        ]
      },
        {
        "id": "dependentlife-1classname",
        "name": "Dependent Life - 1classname",
        "benefits": [
          {
            "key": "spouse",
            "name": "Spouse",
            "values": {
              "Victor": "$10,000",
              "Sunlife": "$10,000",
              "Manulife": "$10,000",
              "Chambers Plan": "$10,000"
            },
            "section": "dependentLife1classname"
          },
          {
            "key": "child",
            "name": "Child",
            "values": {
              "Victor": "$5,000",
              "Sunlife": "$5,000",
              "Manulife": "$5,000",
              "Chambers Plan": "$5,000"
            },
            "section": "dependentLife1classname"
          }
        ]
      },
      {
        "id": "dependentlife-2classname",
        "name": "Dependent Life 2classname",
        "benefits": [
          {
            "key": "spouse",
            "name": "Spouse",
            "values": {
              "Victor": "$10,000",
              "Sunlife": "$10,000",
              "Manulife": "$10,000",
              "Chambers Plan": "$10,000"
            },
            "section": "dependentLife2classname"
          },
          {
            "key": "child",
            "name": "Child",
            "values": {
              "Victor": "$5,000",
              "Sunlife": "$5,000",
              "Manulife": "$5,000",
              "Chambers Plan": "$5,000"
            },
            "section": "dependentLife2classname"
          }
        ]
      },
      
    ]
  },
  {
    "carriers": [
      "Chambers Plan",
      "Manulife",
      "Sunlife",
      "Victor"
    ],
    "sections": [
      {
        "id": "extendedhealth",
        "name": "Extended Health",
        "benefits": [
          {
            "key": "paramedicalMaximum",
            "name": "Paramedical Maximum",
            "values": {
              "Victor": "$500",
              "Sunlife": "$2,500",
              "Manulife": "$500",
              "Chambers Plan": "$500"
            },
            "section": "extendedHealth"
          },
          {
            "key": "psychologistOrSpeechTherapist",
            "name": "Psychologist/Speech Therapist",
            "values": {
              "Victor": "$500",
              "Sunlife": "$600",
              "Manulife": "$600",
              "Chambers Plan": "$600"
            },
            "section": "extendedHealth"
          },
          {
            "key": "visionCare",
            "name": "Vision",
            "values": {
              "Victor": "$200/24 months",
              "Sunlife": "$200/12 months child, 24 months adults",
              "Manulife": "$200/2 years",
              "Chambers Plan": "$200/24 months"
            },
            "section": "extendedHealth"
          },
          {
            "key": "eyeExams",
            "name": "Eye Exams",
            "values": {
              "Victor": "Yes",
              "Sunlife": "Yes",
              "Manulife": "Yes",
              "Chambers Plan": "Yes"
            },
            "section": "extendedHealth"
          },
          {
            "key": "privateDutyNursingMaximum",
            "name": "Private Duty Nursing Maximum",
            "values": {
              "Victor": "$10,000/year",
              "Sunlife": "$25,000/24 months",
              "Manulife": "$10,000/year",
              "Chambers Plan": "$25,000/24 months"
            },
            "section": "extendedHealth"
          },
          {
            "key": "semiPrivateHospital",
            "name": "Semi Private Hospital",
            "values": {
              "Victor": "Yes",
              "Sunlife": "Yes",
              "Manulife": "Yes",
              "Chambers Plan": "Yes"
            },
            "section": "extendedHealth"
          },
          {
            "key": "hearingAids",
            "name": "Hearing Aids",
            "values": {
              "Victor": "$500/3 years",
              "Sunlife": "$700/5 years",
              "Manulife": "$500/5 years",
              "Chambers Plan": "$700/60 months"
            },
            "section": "extendedHealth"
          },
          {
            "key": "outOfCountryTravel",
            "name": "Out of Country Emergency",
            "values": {
              "Victor": "Yes",
              "Sunlife": "Yes",
              "Manulife": "Yes",
              "Chambers Plan": "Yes"
            },
            "section": "extendedHealth"
          },
          {
            "key": "terminationAgeEHC",
            "name": "Termination Age",
            "values": {
              "Victor": "Retirement or to age 80",
              "Sunlife": "Retirement or to age 80",
              "Manulife": "Retirement or to age 85",
              "Chambers Plan": "Retirement or to age 80"
            },
            "section": "extendedHealth"
          }
        ]
      },
      {
        "id": "prescriptiondrugs",
        "name": "Prescription Drugs",
        "benefits": [
          {
            "key": "drugDeductible",
            "name": "Drug Deductible",
            "values": {
              "Victor": "Nil",
              "Sunlife": "Nil",
              "Manulife": "Nil",
              "Chambers Plan": "Nil"
            },
            "section": "prescriptionDrugs"
          }
        ]
      }
    ]
  },
  {
    "carriers": [
      "Chambers Plan",
      "Manulife",
      "Sunlife",
      "Victor"
    ],
    "sections": [
      {
        "id": "prescriptiondrugs",
        "name": "Prescription Drugs",
        "benefits": [
          {
            "key": "prescriptionDrugCoInsurance",
            "name": "Prescription Drug Co-Insurance",
            "values": {
              "Victor": "100%",
              "Sunlife": "100%",
              "Manulife": "100%/50%",
              "Chambers Plan": "100%/50%"
            },
            "section": "prescriptionDrugs"
          },
          {
            "key": "prescriptionMaximum",
            "name": "Prescription Maximum",
            "values": {
              "Victor": "unlimited",
              "Sunlife": "$50,000",
              "Manulife": "$50,000",
              "Chambers Plan": "$50,000"
            },
            "section": "prescriptionDrugs"
          },
          {
            "key": "prescriptionDrugType",
            "name": "Prescription Drug Type",
            "values": {
              "Victor": "Generic",
              "Sunlife": "National Formulary",
              "Manulife": "Generic",
              "Chambers Plan": "National Formulary"
            },
            "section": "prescriptionDrugs"
          },
          {
            "key": "prescriptionPayDirectDrugCard",
            "name": "Pay Direct Drug Card",
            "values": {
              "Victor": "Yes",
              "Sunlife": "Yes",
              "Manulife": "Yes",
              "Chambers Plan": "Yes"
            },
            "section": "prescriptionDrugs"
          },
          {
            "key": "reimbursement",
            "name": "Reimbursement Type",
            "values": {
              "Victor": "Pay Direct",
              "Sunlife": "Pay Direct",
              "Manulife": "Pay Direct",
              "Chambers Plan": "Pay Direct"
            },
            "section": "prescriptionDrugs"
          }
        ]
      },
      {
        "id": "dental",
        "name": "Dental",
        "benefits": [
          {
            "key": "annualDeductibleDental",
            "name": "Annual Deductible",
            "values": {
              "Victor": "Nil",
              "Sunlife": "Nil",
              "Manulife": "Nil",
              "Chambers Plan": "Nil"
            },
            "section": "dental"
          },
          {
            "key": "basicCoInsurance",
            "name": "Basic Co-Insurance",
            "values": {
              "Victor": "80%",
              "Sunlife": "80%",
              "Manulife": "80%",
              "Chambers Plan": "80%"
            },
            "section": "dental"
          },
          {
            "key": "majorCoInsurance",
            "name": "Major Co-Insurance",
            "values": {
              "Victor": "50%",
              "Sunlife": "50%",
              "Manulife": "50%",
              "Chambers Plan": "50%"
            },
            "section": "dental"
          },
          {
            "key": "orthodonticCoInsurance",
            "name": "Orthodontic Co-Insurance",
            "values": {
              "Victor": "-",
              "Sunlife": "-",
              "Manulife": "-",
              "Chambers Plan": "-"
            },
            "section": "dental"
          },
          {
            "key": "basicDentalMaximum",
            "name": "Basic Dental Maximum",
            "values": {
              "Victor": "$1,500",
              "Sunlife": "-",
              "Manulife": "-",
              "Chambers Plan": "-"
            },
            "section": "dental"
          }
        ]
      }
    ]
  }]