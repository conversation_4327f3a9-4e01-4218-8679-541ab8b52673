-- Deployment script for the new plan design multi-class function with groupby logic
-- This script deploys the updated function that groups sections by matching benefits

-- First, backup the original function (optional)
-- CREATE OR REPLACE FUNCTION sandf.fn_get_plan_design_report_multi_class_original AS 
-- (copy of original function for backup)

-- Deploy the new function with groupby logic
\i multi-class/mutli-class.sql

-- Verify the function was created successfully
SELECT 
    routine_name,
    routine_type,
    data_type
FROM information_schema.routines 
WHERE routine_schema = 'sandf' 
AND routine_name = 'fn_get_plan_design_report_multi_class';

-- Test with a sample plan (replace with actual plan UUID)
/*
-- Default pagination (16 sections per page)
SELECT jsonb_pretty(
    sandf.fn_get_plan_design_report_multi_class(
        'your-plan-uuid-here',
        'your-user-id',
        NULL,
        NULL,
        16  -- max_sections_per_page
    )
);

-- Custom pagination (5 sections per page)
SELECT jsonb_pretty(
    sandf.fn_get_plan_design_report_multi_class(
        'your-plan-uuid-here',
        'your-user-id',
        NULL,
        NULL,
        5   -- smaller page size
    )
);
*/

/*
Key changes in the new function:

1. GROUPBY LOGIC:
   - Added class-to-letter mapping (A, B, C...) for groupby logic
   - Collects section data by class first, then groups by matching benefit values
   - Creates combined sections with appropriate suffixes:
     * "ALL" when benefits match across all classes
     * "A", "B", "C" when benefits differ between classes
   - Maintains proper ordering: ALL first, then A, B, C, then combinations
   - Follows the same pattern as rate-sheet-v2.sql for consistency

2. SMART PAGINATION:
   - Added max_sections_per_page parameter (default: 16)
   - Sections are NEVER split across multiple page objects
   - Each section remains complete within one page object
   - Pagination is based on section count, not benefit count
   - If all sections fit in one page, returns single page object
   - Creates multiple page objects only when section count exceeds page limit

3. FUNCTION SIGNATURE:
   fn_get_plan_design_report_multi_class(
       plan_uuid_param TEXT,
       user_id_param TEXT DEFAULT NULL,
       includes_param TEXT[] DEFAULT NULL,
       excludes_param TEXT[] DEFAULT NULL,
       max_sections_per_page INTEGER DEFAULT 16  -- NEW PARAMETER
   )

4. OUTPUT FORMAT CHANGES:
   - Old: "Life Insurance & AD&D - Class 1 - Salaried EE's"
   - New: "Life Insurance & AD&D A" (if benefits differ) or "Life Insurance & AD&D ALL" (if benefits match)

5. PAGINATION BEHAVIOR:
   - Each page object contains: {"carriers": [...], "sections": [...]}
   - Sections array contains complete sections (never partial)
   - Carriers array is included on every page for consistency

This provides the plan-design-new.json format you requested with smart pagination.
*/
