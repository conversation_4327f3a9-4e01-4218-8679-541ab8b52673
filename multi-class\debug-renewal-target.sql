-- Debug version of renewal target function
-- This version has extensive logging to help identify issues

CREATE OR REPLACE FUNCTION sandf.fn_get_renewal_target_debug(
    target_plan_uuid TEXT,
    user_id_param TEXT DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    v_plan_id INTEGER;
    quote_record RECORD;
    employee_classes TEXT[];
    employee_class_count INTEGER;
    carrier_count INTEGER;
    quote_count INTEGER;
BEGIN
    RAISE NOTICE 'Starting renewal target debug for plan: %', target_plan_uuid;
    
    -- Get plan_id from plan_uuid
    SELECT plan_id INTO v_plan_id
    FROM sandf.plan
    WHERE plan_uuid = target_plan_uuid::uuid;

    RAISE NOTICE 'Found plan_id: %', v_plan_id;

    IF v_plan_id IS NULL THEN
        RAISE NOTICE 'Plan not found!';
        RETURN jsonb_build_object('error', 'Plan not found');
    END IF;

    -- Check basic data availability
    SELECT COUNT(*) INTO quote_count
    FROM sandf.plan p
    JOIN sandf.quote q ON q.plan_id = p.plan_id
    WHERE p.plan_uuid = target_plan_uuid::uuid;
    
    RAISE NOTICE 'Total quotes for plan: %', quote_count;

    -- Check employee class quotes
    SELECT COUNT(*) INTO quote_count
    FROM sandf.plan p
    JOIN sandf.quote q ON q.plan_id = p.plan_id
    JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
    WHERE p.plan_uuid = target_plan_uuid::uuid;
    
    RAISE NOTICE 'Total employee class quotes: %', quote_count;

    -- Check formatted quote details
    SELECT COUNT(*) INTO quote_count
    FROM sandf.plan p
    JOIN sandf.quote q ON q.plan_id = p.plan_id
    JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
    WHERE p.plan_uuid = target_plan_uuid::uuid
    AND ecq.formatted_quote_details IS NOT NULL
    AND ecq.formatted_quote_details != '{}'::jsonb;
    
    RAISE NOTICE 'Quotes with formatted details: %', quote_count;

    -- Detect employee classes
    SELECT COUNT(DISTINCT ec.name), array_agg(DISTINCT ec.name ORDER BY ec.name)
    INTO employee_class_count, employee_classes
    FROM sandf.plan p
    JOIN sandf.quote q ON q.plan_id = p.plan_id
    JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
    JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
    WHERE p.plan_uuid = target_plan_uuid::uuid
    AND ecq.formatted_quote_details IS NOT NULL
    AND ecq.formatted_quote_details != '{}'::jsonb;

    RAISE NOTICE 'Found % employee classes: %', employee_class_count, employee_classes;

    -- Check carriers
    SELECT COUNT(DISTINCT c.description) INTO carrier_count
    FROM sandf.plan p
    JOIN sandf.quote q ON q.plan_id = p.plan_id
    JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
    JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
    JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
    WHERE p.plan_uuid = target_plan_uuid::uuid
    AND ec.name = ANY(employee_classes)
    AND ecq.formatted_quote_details IS NOT NULL
    AND ecq.formatted_quote_details != '{}'::jsonb;
    
    RAISE NOTICE 'Found % carriers', carrier_count;

    -- Sample some actual data
    FOR quote_record IN
        SELECT 
            ec.name as employee_class_name,
            c.description as carrier_description,
            ecq.formatted_quote_details
        FROM sandf.plan p
        JOIN sandf.quote q ON q.plan_id = p.plan_id
        JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
        JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
        JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
        WHERE p.plan_uuid = target_plan_uuid::uuid
        AND ec.name = ANY(employee_classes)
        AND ecq.formatted_quote_details IS NOT NULL
        AND ecq.formatted_quote_details != '{}'::jsonb
        LIMIT 3
    LOOP
        RAISE NOTICE 'Sample data - Class: %, Carrier: %', 
            quote_record.employee_class_name, 
            quote_record.carrier_description;
        
        RAISE NOTICE 'Has benefitPremiums: %', 
            (quote_record.formatted_quote_details ? 'benefitPremiums');
            
        RAISE NOTICE 'Has extendedHealthPremium: %', 
            (quote_record.formatted_quote_details #> '{benefitPremiums}' ? 'extendedHealthPremium');
            
        RAISE NOTICE 'Has dentalCarePremium: %', 
            (quote_record.formatted_quote_details #> '{benefitPremiums}' ? 'dentalCarePremium');
            
        RAISE NOTICE 'Has ratingFactors: %', 
            (quote_record.formatted_quote_details #> '{benefitPremiums}' ? 'ratingFactors');
            
        -- Check structure of EHC data
        IF quote_record.formatted_quote_details #> '{benefitPremiums,extendedHealthPremium}' IS NOT NULL THEN
            RAISE NOTICE 'EHC structure: %', 
                quote_record.formatted_quote_details #> '{benefitPremiums,extendedHealthPremium}';
        END IF;
        
        -- Check structure of rating factors
        IF quote_record.formatted_quote_details #> '{benefitPremiums,ratingFactors}' IS NOT NULL THEN
            RAISE NOTICE 'Rating factors structure: %', 
                quote_record.formatted_quote_details #> '{benefitPremiums,ratingFactors}';
        END IF;
    END LOOP;

    RETURN jsonb_build_object(
        'plan_id', v_plan_id,
        'employee_class_count', employee_class_count,
        'employee_classes', to_jsonb(employee_classes),
        'carrier_count', carrier_count,
        'quote_count', quote_count,
        'status', 'debug_complete'
    );

EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Error in debug function: %', SQLERRM;
        RETURN jsonb_build_object('error', SQLERRM);
END;
$$;
