# Plan Design V2 Function - Multi-Class Support

## Overview

The new `fn_get_plan_design_report_v2` function extends your existing plan design functionality to support multiple employee classes dynamically, while maintaining backward compatibility with single-class plans.

## Key Features

### 1. Automatic Employee Class Detection
- Dynamically detects the number of employee classes for a given plan
- No hardcoded employee class names (removes 'RTQ' dependency)
- Supports any number of employee classes

### 2. Adaptive Processing Logic
- **Single Class (count = 1)**: Uses optimized original logic for best performance
- **Multiple Classes (count > 1)**: Processes each class separately and consolidates output

### 3. Enhanced Output Structure for Multi-Class
- Section IDs: `"lifeinsuranceadad-1classname"`, `"dependentlife-2classname"`
- Section Names: `"Life Insurance & AD&D - 1classname"`, `"Dependent Life - 2classname"`
- Section Properties: `"lifeInsuranceADAD1classname"`, `"dependentLife2classname"`
- All classes consolidated into single output object

## Function Signature

```sql
CREATE OR REPLACE FUNCTION fn_get_plan_design_report_v2(
    plan_uuid_param TEXT,
    user_id_param TEXT DEFAULT NULL,
    includes_param TEXT[] DEFAULT NULL,
    excludes_param TEXT[] DEFAULT NULL
)
RETURNS JSONB
```

## Output Examples

### Single Employee Class
```json
[
  {
    "carriers": ["Chambers Plan", "Manulife", "Sunlife", "Victor"],
    "sections": [
      {
        "id": "lifeinsuranceadad",
        "name": "Life Insurance & AD&D",
        "benefits": [...]
      }
    ]
  }
]
```

### Multiple Employee Classes
```json
[
  {
    "carriers": ["Chambers Plan", "Manulife", "Sunlife", "Victor"],
    "sections": [
      {
        "id": "lifeinsuranceadad-1classname",
        "name": "Life Insurance & AD&D - 1classname",
        "benefits": [
          {
            "key": "coverageLife",
            "name": "Amount of Coverage",
            "section": "lifeInsuranceADAD1classname",
            "values": {...}
          }
        ]
      },
      {
        "id": "lifeinsuranceadad-2classname",
        "name": "Life Insurance & AD&D - 2classname",
        "benefits": [...]
      },
      {
        "id": "dependentlife-1classname",
        "name": "Dependent Life - 1classname",
        "benefits": [...]
      },
      {
        "id": "dependentlife-2classname",
        "name": "Dependent Life - 2classname",
        "benefits": [...]
      }
    ]
  }
]
```

## Implementation Details

### Employee Class Detection
```sql
SELECT COUNT(DISTINCT ec.name), array_agg(DISTINCT ec.name ORDER BY ec.name)
INTO employee_class_count, employee_classes
FROM sandf.plan p
JOIN sandf.quote q ON q.plan_id = p.plan_id
JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
WHERE p.plan_uuid = plan_uuid_param::uuid
```

### Class Suffix Generation
- Format: `{class_index}{class_name}`
- Example: `1RTQ`, `2Management`, `3Union`

### Section ID/Name Modification
- Section ID: `{original_section_id}-{class_suffix}`
- Section Name: `{original_section_name} - {class_suffix}`
- Section Property: `{original_group_name}{class_suffix}`

## Benefits

1. **Backward Compatibility**: Single-class plans work exactly as before
2. **Dynamic Scaling**: Automatically handles any number of employee classes
3. **Performance Optimized**: Uses original logic for single-class scenarios
4. **Consistent Output**: Maintains same structure and pagination logic
5. **Template Friendly**: Output structure works well with existing FTL templates

## Migration Guide

### From Old Function
```sql
-- Old
SELECT fn_get_plan_design_report('plan-uuid', 'user-id');

-- New
SELECT fn_get_plan_design_report_v2('plan-uuid', 'user-id');
```

### Key Differences
1. New function returns array format consistently
2. Multi-class support with class-suffixed section names
3. No hardcoded employee class dependencies

## Testing

Use the provided test scripts:
- `test-plan-design-v2-final.sql`: Comprehensive testing
- `deploy-plan-design-v2.sql`: Deployment and verification

## Files Created

1. `multi-class/multi-class-support/plan-design-v2.sql`: Main function
2. `test-plan-design-v2-final.sql`: Test script
3. `deploy-plan-design-v2.sql`: Deployment script
4. `plan-design-v2-summary.md`: This documentation

## Next Steps

1. Execute `deploy-plan-design-v2.sql` to create the function
2. Test with your actual plan UUIDs using the test script
3. Update your application to use the new function
4. Verify output format matches your requirements
