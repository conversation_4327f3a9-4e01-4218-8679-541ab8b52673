-- Test script for the new plan design multi-class function with groupby logic
-- This script tests the groupby functionality that compares benefits across classes

-- Step 1: Check what employee classes exist for your plan
-- Replace 'your-plan-uuid-here' with your actual plan UUID
SELECT 
    p.plan_uuid,
    COUNT(DISTINCT ec.name) as total_employee_classes,
    array_agg(DISTINCT ec.name ORDER BY ec.name) as employee_class_names
FROM sandf.plan p
JOIN sandf.quote q ON q.plan_id = p.plan_id
JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
WHERE p.plan_uuid = 'your-plan-uuid-here'::uuid
AND ecq.formatted_quote_details IS NOT NULL
AND ecq.formatted_quote_details != '{}'::jsonb
GROUP BY p.plan_uuid;

-- Step 2: Test the new multi-class plan design function with groupby logic
-- This should output sections with A, B, ALL labels based on benefit matching
-- Default pagination: 10 benefits per page (same as original)
SELECT jsonb_pretty(
    sandf.fn_get_plan_design_report_multi_class(
        'your-plan-uuid-here',
        'your-user-id',
        NULL, -- includes_param (NULL means include all)
        NULL  -- excludes_param (NULL means exclude none)
    )
);

-- Step 3: Test with specific includes to see groupby logic on specific sections
-- Example: Test only life insurance and dental sections
SELECT jsonb_pretty(
    sandf.fn_get_plan_design_report_multi_class(
        'your-plan-uuid-here',
        'your-user-id',
        ARRAY['lifeInsuranceADAD', 'dental'], -- Only these sections
        NULL
    )
);

-- Step 4: Compare with original multi-class output (before groupby)
-- You can run the original function to see the difference
-- SELECT jsonb_pretty(
--     sandf.fn_get_plan_design_report_multi_class_original(
--         'your-plan-uuid-here',
--         'your-user-id'
--     )
-- );

/*
Expected behavior of the new groupby logic:

1. GROUPBY LOGIC:
   - If benefits match across ALL employee classes:
     Section will have "ALL" suffix (e.g., "dependentlifeALL", "Dependent Life ALL")
   - If benefits don't match across classes:
     Sections will be grouped by matching patterns with A, B, C... labels
     Example: "lifeinsuranceadadA", "Life Insurance & AD&D A" for first group
     Example: "lifeinsuranceadadB", "Life Insurance & AD&D B" for second group

2. SECTION ORDERING:
   - ALL sections come first
   - Then single letter sections (A, B, C...)
   - Then combination sections (A,B, A,C...)

3. BENEFIT COMPARISON:
   - Benefits within sections are compared by their values across all carriers
   - If benefit values match exactly across classes, they get grouped together
   - If they differ, they get separate A/B/C labels

4. SMART PAGINATION (Enhanced from original multi-class-old.sql):
   - Uses same pagination logic as original: MAX_BENEFITS_PER_PAGE = 10
   - Pagination is based on benefit count, not section count
   - ENHANCED: Sections are NEVER split across multiple page objects
   - Before adding a section, checks if entire section fits in current page
   - If section would exceed page limit, creates new page first
   - Each section remains complete within one page object
   - Each page object contains: carriers array + complete sections array

5. PAGINATION EXAMPLES:
   - 8 benefits total → 1 page object with all sections
   - 15 benefits total → 2 page objects (benefits distributed but sections kept whole)
   - Section with 12 benefits → Gets its own page (won't be split)

This matches the logic from rate-sheet-v2.sql but applied to plan design output.
*/
