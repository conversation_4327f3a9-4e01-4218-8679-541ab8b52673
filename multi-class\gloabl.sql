-- Global function to route plan design requests to appropriate implementation
-- Routes to single-class (RTQ) or multi-class function based on employee class data
CREATE OR REPLACE FUNCTION sandf.fn_get_plan_design_global(   
    plan_uuid_param TEXT,
    user_id_param TEXT DEFAULT NULL,
    includes_param TEXT[] DEFAULT NULL,
    excludes_param TEXT[] DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    employee_class_count INTEGER;
    employee_classes TEXT[];
    has_rtq_only BOOLEAN := FALSE;
    result JSONB;
BEGIN
    -- Step 1: Detect employee classes for this plan
    SELECT COUNT(DISTINCT ec.name), array_agg(DISTINCT ec.name ORDER BY ec.name)
    INTO employee_class_count, employee_classes
    FROM sandf.plan p
    JOIN sandf.quote q ON q.plan_id = p.plan_id
    JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
    JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
    WHERE p.plan_uuid = plan_uuid_param::uuid
    AND ecq.formatted_quote_details IS NOT NULL
    AND ecq.formatted_quote_details != '{}'::jsonb;

    -- Log the detected employee classes for debugging
    RAISE NOTICE 'Global function detected % employee classes: %', employee_class_count, employee_classes;

    -- Step 2: Check if we have only RTQ employee class
    IF employee_class_count = 1 AND employee_classes[1] = 'RTQ' THEN
        has_rtq_only := TRUE;
    END IF;

    -- Step 3: Route to appropriate function based on employee class data
    IF has_rtq_only THEN
        -- Single RTQ employee class - use original plan-design function
        RAISE NOTICE 'Routing to single-class plan design function (RTQ only)';

        -- Call the original plan design function
        SELECT sandf.fn_get_plan_design_report_data_v4(
            plan_uuid_param,
            user_id_param,
            includes_param,
            excludes_param
        ) INTO result;

    ELSE
        -- Multiple employee classes or non-RTQ - use multi-class function
        RAISE NOTICE 'Routing to multi-class plan design function (% classes)', employee_class_count;

        -- Call the multi-class plan design function
        SELECT sandf.fn_get_plan_design_report_multi_class(
            plan_uuid_param,
            user_id_param,
            includes_param,
            excludes_param
        ) INTO result;

    END IF;

    -- Step 4: Return the result from the appropriate function
    RETURN result;

EXCEPTION
    WHEN OTHERS THEN
        -- Handle any errors and provide meaningful error information
        RAISE EXCEPTION 'Error in global plan design function: % (SQLSTATE: %)', SQLERRM, SQLSTATE;

END;
$$;

-- Global function to resolve employee class for any plan
-- Returns the appropriate employee class to use: RTQ if single RTQ class, otherwise first employee class
CREATE OR REPLACE FUNCTION sandf.fn_resolve_employee_class_global(
    plan_uuid_param TEXT
)
RETURNS TABLE(
    employee_class_name TEXT,
    employee_class_id INTEGER,
    employee_class_count INTEGER,
    all_employee_classes TEXT[],
    is_rtq_only BOOLEAN,
    resolution_strategy TEXT
)
LANGUAGE plpgsql
AS $$
DECLARE
    v_plan_id INTEGER;
    detected_employee_class_count INTEGER;
    detected_employee_classes TEXT[];
    resolved_employee_class_name TEXT;
    resolved_employee_class_id INTEGER;
    is_single_rtq BOOLEAN := FALSE;
    strategy_used TEXT;
BEGIN
    -- Step 1: Get plan_id
    SELECT p.plan_id INTO v_plan_id
    FROM sandf.plan p
    WHERE p.plan_uuid = plan_uuid_param::uuid
    ORDER BY p.plan_id ASC
    LIMIT 1;

    IF v_plan_id IS NULL THEN
        RAISE EXCEPTION 'Plan not found for UUID: %', plan_uuid_param;
    END IF;

    -- Step 2: Detect all employee classes for this plan
    SELECT COUNT(DISTINCT ec.name), array_agg(DISTINCT ec.name ORDER BY ec.name)
    INTO detected_employee_class_count, detected_employee_classes
    FROM sandf.plan p
    JOIN sandf.quote q ON q.plan_id = p.plan_id
    JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
    JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
    WHERE p.plan_uuid = plan_uuid_param::uuid
    AND ecq.formatted_quote_details IS NOT NULL
    AND ecq.formatted_quote_details != '{}'::jsonb;

    -- Step 3: Apply resolution logic
    IF detected_employee_class_count = 1 AND detected_employee_classes[1] = 'RTQ' THEN
        -- Single RTQ employee class - use RTQ
        resolved_employee_class_name := 'RTQ';
        is_single_rtq := TRUE;
        strategy_used := 'SINGLE_RTQ';

        RAISE NOTICE 'Employee class resolution: Using single RTQ class';

    ELSE
        -- Multiple employee classes or non-RTQ - use first employee class (multi-class approach)
        resolved_employee_class_name := detected_employee_classes[1];
        is_single_rtq := FALSE;
        strategy_used := 'MULTI_CLASS_FIRST_OBJECT';

        RAISE NOTICE 'Employee class resolution: Using first employee class (%) from % total classes',
                     resolved_employee_class_name, detected_employee_class_count;
    END IF;

    -- Step 4: Get the employee_class_id for the resolved employee class
    SELECT ec.employee_class_id INTO resolved_employee_class_id
    FROM sandf.employee_class ec
    WHERE ec.plan_id = v_plan_id
    AND ec.name = resolved_employee_class_name
    ORDER BY ec.employee_class_id ASC
    LIMIT 1;

    -- Step 5: Return the resolved information
    RETURN QUERY SELECT
        resolved_employee_class_name,
        resolved_employee_class_id,
        detected_employee_class_count,
        detected_employee_classes,
        is_single_rtq,
        strategy_used;

EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Error in global employee class resolver: % (SQLSTATE: %)', SQLERRM, SQLSTATE;
END;
$$;

-- Simple wrapper function that returns just the employee class name (most commonly used)
    CREATE OR REPLACE FUNCTION sandf.fn_get_resolved_employee_class(
        plan_uuid_param TEXT
    )
    RETURNS TEXT
    LANGUAGE plpgsql
    AS $$
    DECLARE
        result_name TEXT;
    BEGIN
        SELECT employee_class_name INTO result_name
        FROM sandf.fn_resolve_employee_class_global(plan_uuid_param)
        LIMIT 1;

        RETURN result_name;
    END;
    $$;

-- Function to get employee class ID (for functions that need the ID)
CREATE OR REPLACE FUNCTION sandf.fn_get_resolved_employee_class_id(
    plan_uuid_param TEXT
)
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
    result_id INTEGER;
BEGIN
    SELECT employee_class_id INTO result_id
    FROM sandf.fn_resolve_employee_class_global(plan_uuid_param)
    LIMIT 1;

    RETURN result_id;
END;
$$;

