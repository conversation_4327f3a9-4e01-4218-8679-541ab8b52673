-- Query to check employee class data structure for a specific plan
-- Replace 'your-plan-uuid-here' with your actual plan UUID

SELECT 
    p.plan_uuid,
    ec.name as employee_class_name,
    ec.employee_class_id,
    c.description as carrier_description,
    q.quote_id,
    q.quote_uuid,
    CASE 
        WHEN ecq.formatted_quote_details IS NOT NULL THEN 'formatted_quote_details available'
        ELSE 'formatted_quote_details NULL'
    END as formatted_data_status,
    CASE 
        WHEN ecq.quote_details IS NOT NULL THEN 'quote_details available'
        ELSE 'quote_details NULL'
    END as quote_data_status
FROM sandf.plan p
JOIN sandf.quote q ON q.plan_id = p.plan_id
JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
WHERE p.plan_uuid = 'your-plan-uuid-here'::uuid
ORDER BY ec.name, c.description;

-- Query to count employee classes for a plan
SELECT 
    p.plan_uuid,
    COUNT(DISTINCT ec.name) as total_employee_classes,
    array_agg(DISTINCT ec.name ORDER BY ec.name) as employee_class_names
FROM sandf.plan p
JOIN sandf.quote q ON q.plan_id = p.plan_id
JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
WHERE p.plan_uuid = 'your-plan-uuid-here'::uuid
GROUP BY p.plan_uuid;

-- Query to see sample data structure from formatted_quote_details
SELECT 
    ec.name as employee_class_name,
    c.description as carrier_description,
    jsonb_pretty(ecq.formatted_quote_details) as sample_data
FROM sandf.plan p
JOIN sandf.quote q ON q.plan_id = p.plan_id
JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
WHERE p.plan_uuid = 'your-plan-uuid-here'::uuid
AND ecq.formatted_quote_details IS NOT NULL
AND ecq.formatted_quote_details != '{}'::jsonb
LIMIT 2;
