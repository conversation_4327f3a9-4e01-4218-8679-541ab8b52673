# Debug Guide for Rate Sheet v2 Calculations

I've added comprehensive RAISE NOTICE statements throughout the `rate-sheet-v2.sql` function to help debug the calculation logic. Here's what each debug statement will show you:

## Debug Output Structure

### 1. Initial Setup
- **Employee Classes**: Shows how many employee classes were found and their names
- **Processing Start**: Indicates when calculation processing begins

### 2. Employee Class Processing
- **Current Class**: Shows which employee class is being processed
- **Carrier Processing**: Shows which carrier is being processed for each class

### 3. Benefit Processing
- **Benefit Keys**: Shows each benefit key being processed and its raw value
- **Key Mapping**: Shows how benefit keys are mapped using the config
- **Include/Exclude Logic**: Shows whether each benefit should be included in calculations

### 4. Premium Calculations
- **Nested Structure**: For benefits with single/couple/family structure
  - Shows coverage type being processed
  - Shows raw premium values
  - Shows parsed numeric values
  - Shows running totals after each addition
- **Direct Premiums**: For benefits with direct premium values
  - Shows raw premium values
  - Shows parsed numeric values
  - Shows running totals after each addition

### 5. Carrier Total Accumulation
- **Class Totals**: Shows monthly/annual totals for each carrier per class
- **Carrier Accumulation**: Shows how totals are accumulated across classes
- **Storage**: Shows final stored totals for each carrier

### 6. Final Calculations
- **Premium Maps**: Shows the final monthly and annual premium maps
- **First Carrier**: Shows which carrier is used as the baseline (#1)
- **Calculation Building**: For each carrier shows:
  - Monthly and annual premium amounts
  - Dollar difference calculations
  - Percentage difference calculations
  - Final calculation object structure

### 7. Results
- **Final Array**: Shows the complete calculations array
- **Count**: Shows total number of calculations

## How to Use This Debug Information

1. **Run the function** with your plan UUID
2. **Check the PostgreSQL logs** or console output for RAISE NOTICE messages
3. **Look for patterns** in the debug output:
   - Are all expected benefits being processed?
   - Are premium values being parsed correctly?
   - Are totals accumulating properly across employee classes?
   - Are the final calculations formatted correctly?

## Common Issues to Look For

1. **Missing Benefits**: If certain benefits aren't showing in the debug output
2. **Zero Premiums**: If premium values are showing as 0 when they shouldn't be
3. **Incorrect Mapping**: If benefit keys aren't being mapped correctly
4. **Include/Exclude Issues**: If benefits are being incorrectly included or excluded
5. **Accumulation Problems**: If totals aren't adding up correctly across classes
6. **Formatting Issues**: If final calculations have incorrect formatting

## Example Debug Output Pattern

```
NOTICE: Starting calculations for 2 employee classes: {" Class 1 - Salaried EE's"," Class 2 - Hourly EE's"}
NOTICE: Processing employee class:  Class 1 - Salaried EE's
NOTICE: Processing carrier: Carrier A for class:  Class 1 - Salaried EE's
NOTICE: Initialized totals - Monthly: 0, Annual: 0
NOTICE: Processing benefit key: extendedHealth with value: {"single":{"premium":"100.00"},"couple":{"premium":"200.00"}}
NOTICE: Key: extendedHealth mapped to: extendedHealth
NOTICE: Should include key extendedHealth: true
NOTICE: Processing nested structure for key: extendedHealth
NOTICE: Coverage Type: single, Subval: {"premium":"100.00"}
NOTICE: Parsed Premium Value for single: 100
NOTICE: Running Total - Monthly: 100, Annual: 1200
...
```

This debug information will help you identify exactly where the calculation logic might be failing or producing unexpected results.
