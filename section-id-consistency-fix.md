# Section ID Consistency Fix

## Problem Identified

The issue in your JSON files (t1.json vs t2.json) was caused by inconsistent section ID generation in the `plan-design-multi-class.sql` file. The problem was:

1. **Benefit Section References**: When creating benefits, the section reference was generated using one formula
2. **Section IDs**: When creating sections, the section ID was generated using a slightly different formula
3. **Mismatch**: This caused benefits to reference section IDs that didn't exactly match the actual section IDs

## Root Cause

The inconsistency was in the string replacement logic:

### Before Fix:
- **Benefit creation** (line 237): `lower(replace(replace(group_name, ' ', ''), '&', ''))`
- **Section creation** (line 389): `lower(replace(replace(section_order_record.original_section_name, ' ', ''), '&', ''))`

The issue was that apostrophes (`'`) were not being handled consistently between the two operations.

### After Fix:
- **Benefit creation**: `lower(replace(replace(replace(group_name, ' ', ''), '&', ''), '''', ''))`
- **Section creation**: `lower(replace(replace(replace(section_order_record.original_section_name, ' ', ''), '&', ''), '''', ''))`

## Changes Made

### File: `multi-class/function-plan-design/plan-design-multi-class.sql`

1. **Line 237** (benefit creation for group-based benefits):
   - Added apostrophe removal: `replace(group_name, '''', '')`

2. **Line 348** (benefit creation for key-based benefits):
   - Added apostrophe removal: `replace(benefit_key, '''', '')`

3. **Line 389** (section ID generation):
   - Added apostrophe removal: `replace(section_order_record.original_section_name, '''', '')`

## Expected Results

After this fix:
- Section IDs like `lifeinsuranceadad-class1-salariedees` will be generated consistently
- Benefit section references will match exactly with section IDs
- No more mismatched section references in the JSON output

## Testing

Run the test script `test-section-id-consistency.sql` to verify the fix works correctly for various test cases including:
- Life Insurance & AD&D with apostrophes in class names
- Different benefit types
- Various class suffix formats

## Impact

This fix ensures that:
1. All benefits are properly associated with their sections
2. The JSON output structure is consistent
3. Frontend rendering will work correctly with proper section-benefit relationships
