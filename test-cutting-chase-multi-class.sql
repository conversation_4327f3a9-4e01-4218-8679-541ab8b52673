-- Test script for the new multi-class cutting-chase function
-- This script tests both the multi-class implementation and the global wrapper

-- Step 1: Test employee class detection for your plan
SELECT 
    p.plan_uuid,
    COUNT(DISTINCT ec.name) as total_employee_classes,
    array_agg(DISTINCT ec.name ORDER BY ec.name) as employee_class_names,
    CASE 
        WHEN COUNT(DISTINCT ec.name) = 1 AND array_agg(DISTINCT ec.name ORDER BY ec.name)[1] = 'RTQ' THEN 'Single RTQ'
        ELSE 'Multi-class or non-RTQ'
    END as scenario_type
FROM sandf.plan p
JOIN sandf.quote q ON q.plan_id = p.plan_id
JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
WHERE p.plan_uuid = 'your-plan-uuid-here'::uuid  -- Replace with your actual plan UUID
AND ecq.formatted_quote_details IS NOT NULL
AND ecq.formatted_quote_details != '{}'::jsonb
GROUP BY p.plan_uuid;

-- Step 2: Test the multi-class cutting-chase function directly
SELECT jsonb_pretty(
    sandf.fn_get_cutting_chase_multi_class(
        'your-plan-uuid-here',  -- Replace with your actual plan UUID
        'your-user-id'          -- Replace with your user ID (optional)
    )::jsonb
);

-- Step 3: Test the global wrapper function
SELECT jsonb_pretty(
    sandf.fn_get_cutting_chase_global(
        'your-plan-uuid-here',  -- Replace with your actual plan UUID
        'your-user-id'          -- Replace with your user ID (optional)
    )::jsonb
);

-- Step 4: Compare carrier totals across employee classes
-- This query shows the raw data that the function processes
WITH carrier_totals AS (
    SELECT 
        ec.name as employee_class,
        c.description as carrier_name,
        (ecq.formatted_quote_details -> 'benefitPremiums' ->> 'totalMonthlyPremiums')::NUMERIC as monthly_premium
    FROM sandf.plan p
    JOIN sandf.quote q ON q.plan_id = p.plan_id
    JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
    JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
    JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
    WHERE p.plan_uuid = 'your-plan-uuid-here'::uuid  -- Replace with your actual plan UUID
    AND ecq.formatted_quote_details IS NOT NULL
    AND ecq.formatted_quote_details != '{}'::jsonb
    AND (ecq.formatted_quote_details -> 'benefitPremiums' ->> 'totalMonthlyPremiums') IS NOT NULL
    ORDER BY 
        sandf.get_user_preference_order(
            'your-user-id',  -- Replace with your user ID
            'your-plan-uuid-here',
            q.quote_id,
            q.quote_uuid,
            999999
        ) ASC,
        c.description ASC
)
SELECT 
    carrier_name,
    SUM(monthly_premium) as total_monthly_across_classes,
    SUM(monthly_premium) * 12 as total_annual_across_classes,
    array_agg(DISTINCT employee_class ORDER BY employee_class) as employee_classes,
    jsonb_object_agg(employee_class, monthly_premium) as breakdown_by_class
FROM carrier_totals
GROUP BY carrier_name
ORDER BY MIN(monthly_premium) ASC;  -- Order by lowest premium first (like the function does)

-- Step 5: Verify rankings calculation
-- This shows how carriers should be ranked
SELECT 
    ROW_NUMBER() OVER (
        ORDER BY 
            sandf.get_user_preference_order(
                'your-user-id',  -- Replace with your user ID
                'your-plan-uuid-here',
                q.quote_id,
                q.quote_uuid,
                999999
            ) ASC,
            c.description ASC
    ) as expected_rank,
    c.description as carrier_name,
    sandf.get_user_preference_order(
        'your-user-id',  -- Replace with your user ID
        'your-plan-uuid-here',
        q.quote_id,
        q.quote_uuid,
        999999
    ) as user_preference_order
FROM sandf.plan p
JOIN sandf.quote q ON q.plan_id = p.plan_id
JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
WHERE p.plan_uuid = 'your-plan-uuid-here'::uuid  -- Replace with your actual plan UUID
AND ecq.formatted_quote_details IS NOT NULL
AND ecq.formatted_quote_details != '{}'::jsonb
GROUP BY c.description, q.quote_id, q.quote_uuid
ORDER BY 
    sandf.get_user_preference_order(
        'your-user-id',  -- Replace with your user ID
        'your-plan-uuid-here',
        q.quote_id,
        q.quote_uuid,
        999999
    ) ASC,
    c.description ASC;
