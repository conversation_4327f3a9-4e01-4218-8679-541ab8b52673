-- Test script for the new dynamic multi-class plan design function v2
-- This script tests the function with automatic employee class detection

-- Step 1: Check what employee classes exist for your plan
-- Replace 'your-plan-uuid-here' with your actual plan UUID
SELECT 
    p.plan_uuid,
    COUNT(DISTINCT ec.name) as total_employee_classes,
    array_agg(DISTINCT ec.name ORDER BY ec.name) as employee_class_names
FROM sandf.plan p
JOIN sandf.quote q ON q.plan_id = p.plan_id
JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
WHERE p.plan_uuid = 'your-plan-uuid-here'::uuid
AND ecq.formatted_quote_details IS NOT NULL
AND ecq.formatted_quote_details != '{}'::jsonb
GROUP BY p.plan_uuid;

-- Step 2: Test the new dynamic function
-- This will automatically detect if it's single or multi-class and handle accordingly
SELECT jsonb_pretty(
    sandf.fn_get_plan_design_report_multi_v2(
        'your-plan-uuid-here',  -- Replace with your actual plan UUID
        'your-user-id',         -- Replace with your user ID (optional)
        NULL,                   -- includes_param (optional)
        NULL                    -- excludes_param (optional)
    )
);

-- Step 3: Test with specific includes/excludes
SELECT jsonb_pretty(
    sandf.fn_get_plan_design_report_multi_v2(
        'your-plan-uuid-here',
        'your-user-id',
        ARRAY['lifeInsuranceADAD', 'dental'],  -- Only include these groups
        ARRAY['excludeGroup']                   -- Exclude these groups
    )
);

-- Step 4: Compare with old function (for single class plans only)
-- This helps verify that single-class behavior is preserved
SELECT 'OLD FUNCTION OUTPUT (single class only):' as comparison;
SELECT jsonb_pretty(
    sandf.fn_get_plan_design_report(
        'your-plan-uuid-here',
        'your-user-id',
        NULL,
        NULL
    )
);

-- Step 5: Performance comparison (optional)
-- Uncomment to compare performance between old and new functions:

/*
-- Time the old function (single class only)
\timing on
SELECT sandf.fn_get_plan_design_report('your-plan-uuid-here', 'your-user-id', NULL, NULL);
\timing off

-- Time the new function
\timing on
SELECT sandf.fn_get_plan_design_report_multi_v2('your-plan-uuid-here', 'your-user-id', NULL, NULL);
\timing off
*/

-- Expected output structure for multi-class:
-- [
--   {
--     "carriers": ["Carrier1", "Carrier2", ...],
--     "sections": [
--       {
--         "id": "lifeinsuranceadad-1rtq",
--         "name": "Life Insurance & AD&D - 1RTQ",
--         "benefits": [...]
--       },
--       {
--         "id": "lifeinsuranceadad-2management", 
--         "name": "Life Insurance & AD&D - 2Management",
--         "benefits": [...]
--       },
--       {
--         "id": "dependentlife-1rtq",
--         "name": "Dependent Life - 1RTQ", 
--         "benefits": [...]
--       },
--       {
--         "id": "dependentlife-2management",
--         "name": "Dependent Life - 2Management",
--         "benefits": [...]
--       }
--     ]
--   }
-- ]

-- Notes:
-- 1. The new function automatically detects single vs multi-class scenarios
-- 2. For single class: Uses optimized original logic (no class suffixes)
-- 3. For multi-class: Appends class index + class name to section IDs and names
-- 4. Output structure is consistent with your requirements
-- 5. All existing filtering and ordering capabilities are preserved
-- 6. No hardcoded employee class names - works with any employee class structure
