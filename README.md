# Rate Per Employee Function Documentation

## Overview
The `sandf.fn_get_rates_per_employee` function calculates premium rates per employee based on configurable business rules. It supports different calculation methods for various premium types including tiered rates, per-employee rates, and shared cost calculations.

## Function Signature
```sql
CREATE OR REPLACE FUNCTION sandf.fn_get_rates_per_employee(
    plan_uuid_param TEXT,
    user_id_param BIGINT,
    number_of_employees_param INTEGER,
    includes_param TEXT[] DEFAULT NULL,
    excludes_param TEXT[] DEFAULT NULL
)
RETURNS JSONB
```

## Configuration Structure

The function reads configuration from `config.json_storage` table with the following JSON structure:

```json
{
  "benefitToPremium": {
    "dentalCarePremium": "dental",
    "dependentLifePremium": "dependentLife",
    "lifeInsurancePremium": "lifeInsuranceADAD",
    "extendedHealthPremium": "extendedHealth",
    "criticalIllnessPremium": "criticalIllness",
    "employeeAssistancePremium": "employeeAssistance",
    "longTermDisabilityPremium": "longTermDisability",
    "accidentalDeathAndDismembermentPremium": "lifeInsuranceADAD"
  },
  "included_premium_per_ee": [
    "employeeAssistance"
  ],
  "excluded_premium_shared_cost_rates_per_employee": [
    "dependentLife"
  ]
}
```

### Configuration Properties

1. **`benefitToPremium`**: Maps premium keys from quote data to standardized benefit names
2. **`included_premium_per_ee`**: Array of premiums that should be added equally to all rate types (single, couple, family)
3. **`excluded_premium_shared_cost_rates_per_employee`**: Array of premiums that should NOT be included in shared cost calculations

## Premium Calculation Logic

### 1. Tiered Premiums (single, couple, family)
- Premiums with explicit rate structures for different coverage types
- Each rate type gets its corresponding premium value
- **Special handling for Extended Health and Dental**: If these benefits are listed in `included_premium_per_ee`, their single rate value is also added to all rate types

### 2. Included Premium Per Employee
- Premiums listed in `included_premium_per_ee` are added equally to all rate types
- Example: Employee Assistance premiums are typically the same regardless of coverage type

### 3. Excluded from Shared Cost
- Premiums listed in `excluded_premium_shared_cost_rates_per_employee` are not included in shared cost calculations
- Example: Dependent Life premiums might be excluded from shared costs

### 4. Shared/Flat Premiums
- All other premiums are treated as shared costs
- These are divided by the number of employees and added to each rate type

## Usage Examples

### Basic Usage
```sql
SELECT sandf.fn_get_rates_per_employee(
    'your-plan-uuid',
    123,  -- user_id
    50,   -- number_of_employees
    NULL, -- includes (all benefits)
    NULL  -- excludes (none)
);
```

### With Specific Includes
```sql
SELECT sandf.fn_get_rates_per_employee(
    'your-plan-uuid',
    123,
    50,
    ARRAY['dental', 'extendedHealth'], -- only these benefits
    NULL
);
```

### With Excludes
```sql
SELECT sandf.fn_get_rates_per_employee(
    'your-plan-uuid',
    123,
    50,
    NULL,
    ARRAY['dependentLife'] -- exclude this benefit
);
```

## Installation Steps

1. **Create the function**:
   ```sql
   -- Run the complete-rate-per-ee-function.sql file
   ```

2. **Insert configuration**:
   ```sql
   -- Run the insert-config.sql file
   ```

3. **Verify installation**:
   ```sql
   SELECT sandf.fn_get_rates_per_employee(
       'test-plan-uuid',
       1,
       10,
       NULL,
       NULL
   );
   ```

## Return Format

The function returns a JSONB object with the following structure:

```json
{
  "carriers": ["Carrier1", "Carrier2"],
  "sections": [
    {
      "name": "Rates Per Employee",
      "id": "ratesPerEmployee",
      "benefits": [
        {
          "name": "Single",
          "key": "single",
          "values": {
            "Carrier1": "$123.45",
            "Carrier2": "$134.56"
          }
        },
        {
          "name": "Couple",
          "key": "couple",
          "values": {
            "Carrier1": "$234.56",
            "Carrier2": "$245.67"
          }
        },
        {
          "name": "Family",
          "key": "family",
          "values": {
            "Carrier1": "$345.67",
            "Carrier2": "$356.78"
          }
        }
      ]
    }
  ]
}
```

## Troubleshooting

- Check the function logs using `RAISE NOTICE` statements for debugging
- Verify configuration is properly loaded in `config.json_storage`
- Ensure all required database tables and functions exist (e.g., `sandf.safe_parse_numeric`, `sandf.fn_format_currency_with_symbol_java_style`)
