-- Deployment script for the new dynamic multi-class plan design function v2
-- Execute this script to create the new function in your database

-- Step 1: Create the function
-- This will read from the plan-design-v2.sql file
\i multi-class/multi-class-support/plan-design-v2.sql

-- Step 2: Verify the function was created successfully
SELECT 
    routine_name,
    routine_type,
    data_type,
    routine_definition IS NOT NULL as has_definition
FROM information_schema.routines 
WHERE routine_name = 'fn_get_plan_design_report_multi_v2'
AND routine_schema = 'sandf';

-- Step 3: Test with a sample plan (replace with your actual plan UUID)
-- Uncomment and modify the following lines to test:

/*
-- Check employee classes for your plan
SELECT 
    p.plan_uuid,
    COUNT(DISTINCT ec.name) as total_employee_classes,
    array_agg(DISTINCT ec.name ORDER BY ec.name) as employee_class_names
FROM sandf.plan p
JOIN sandf.quote q ON q.plan_id = p.plan_id
JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
WHERE p.plan_uuid = 'your-plan-uuid-here'::uuid
AND ecq.formatted_quote_details IS NOT NULL
AND ecq.formatted_quote_details != '{}'::jsonb
GROUP BY p.plan_uuid;

-- Test the new function
SELECT jsonb_pretty(
    sandf.fn_get_plan_design_report_multi_v2(
        'your-plan-uuid-here',
        'your-user-id',
        NULL,
        NULL
    )
);
*/

-- Step 4: Performance comparison (optional)
-- Uncomment to compare performance between old and new functions:

/*
-- Time the old function (single class only)
\timing on
SELECT sandf.fn_get_plan_design_report('your-plan-uuid-here', 'your-user-id', NULL, NULL);
\timing off

-- Time the new function
\timing on
SELECT sandf.fn_get_plan_design_report_multi_v2('your-plan-uuid-here', 'your-user-id', NULL, NULL);
\timing off
*/

-- Key Features of the New Function:
-- 1. Automatic employee class detection - no hardcoded 'RTQ' dependency
-- 2. Dynamic processing - handles any number of employee classes
-- 3. Optimized single-class path - uses original logic for single class scenarios
-- 4. Multi-class support - appends class suffixes to section IDs and names
-- 5. Consistent output structure - maintains same pagination and ordering logic
-- 6. Backward compatibility - works with existing single-class plans

-- Migration Notes:
-- 1. Replace calls to fn_get_plan_design_report with fn_get_plan_design_report_v2
-- 2. The new function returns the same structure but with dynamic class support
-- 3. For single class plans, behavior is identical to the original function
-- 4. For multi-class plans, sections will have class suffixes (e.g., "1RTQ", "2Management")
-- 5. All existing filtering (includes/excludes) and ordering capabilities are preserved
