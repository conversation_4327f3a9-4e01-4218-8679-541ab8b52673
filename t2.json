[{"carriers": ["Equitable", "Canada Life", "Benefits by Design", "Manulife", "Sunlife"], "sections": [{"id": "lifeinsuranceadad-class1-salariedees", "name": "Life Insurance & AD&D -  Class 1 - Salaried EE's", "benefits": [{"key": "coverageLife", "name": "Amount of Coverage", "values": {"Sunlife": "$25,000", "Manulife": "$25,000", "Equitable": "2 times annual earnings", "Canada Life": "2 times annual earnings", "Benefits by Design": "$25,000"}, "section": "lifeinsuranceadad-class1-salariedees"}, {"key": "ageReduction", "name": "Age Reduction", "values": {"Sunlife": "50% at age 65", "Manulife": "50% at age 65", "Equitable": "70", "Canada Life": "50% at age 65", "Benefits by Design": "50% at age 65, $25,000 age 70"}, "section": "lifeinsuranceadad-class1-salariedees"}, {"key": "terminationAgeLife", "name": "Termination Age - Life", "values": {"Sunlife": "Retirement or to age 70", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "Retirement or to age 75"}, "section": "lifeinsuranceadad-class1-salariedees"}, {"key": "terminationAgeADAD", "name": "Termination Age - AD&D", "values": {"Sunlife": "Retirement or to age 70", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "Retirement or to age 75"}, "section": "lifeinsuranceadad-class1-salariedees"}]}, {"id": "lifeinsuranceadad-class2-hourlyees", "name": "Life Insurance & AD&D -  Class 2 - Hourly EE's", "benefits": [{"key": "coverageLife", "name": "Amount of Coverage", "values": {"Sunlife": "$25,000", "Manulife": "$25,000", "Equitable": "$25,000", "Canada Life": "2 times annual earnings", "Benefits by Design": "$25,000"}, "section": "lifeinsuranceadad-class2-hourlyees"}, {"key": "ageReduction", "name": "Age Reduction", "values": {"Sunlife": "50% at age 65", "Manulife": "50% at age 65", "Equitable": "50% at age 65", "Canada Life": "65% at age 65, 50% at age 70", "Benefits by Design": "50% at age 65, $25,000 age 70"}, "section": "lifeinsuranceadad-class2-hourlyees"}, {"key": "terminationAgeLife", "name": "Termination Age - Life", "values": {"Sunlife": "Retirement or to age 70", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "Retirement or to age 75"}, "section": "lifeinsuranceadad-class2-hourlyees"}, {"key": "terminationAgeADAD", "name": "Termination Age - AD&D", "values": {"Sunlife": "Retirement or to age 70", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "Retirement or to age 75"}, "section": "lifeinsuranceadad-class2-hourlyees"}]}, {"id": "dependentlife-class1-salariedees", "name": "Dependent Life -  Class 1 - Salaried EE's", "benefits": [{"key": "spouse", "name": "Spouse", "values": {"Canada Life": "-"}, "section": "dependentlife-class1-salariedees"}, {"key": "child", "name": "Child", "values": {"Canada Life": "-"}, "section": "dependentlife-class1-salariedees"}]}]}, {"sections": [{"id": "dependentlife-class2-hourlyees", "name": "Dependent Life -  Class 2 - Hourly EE's", "benefits": [{"key": "spouse", "name": "Spouse", "values": {"Equitable": "-", "Canada Life": "-"}, "section": "dependentlife-class2-hourlyees"}, {"key": "child", "name": "Child", "values": {"Equitable": "-", "Canada Life": "-"}, "section": "dependentlife-class2-hourlyees"}]}, {"id": "extendedhealth-class1-salariedees", "name": "Extended Health -  Class 1 - Salaried EE's", "benefits": [{"key": "annualDeductibleEHC", "name": "Annual Deductible", "values": {"Sunlife": "<PERSON>l", "Manulife": "<PERSON>l", "Equitable": "$25", "Canada Life": "$50", "Benefits by Design": "<PERSON>l"}, "section": "extendedhealth-class1-salariedees"}, {"key": "coInsuranceEHC", "name": "CoInsurance", "values": {"Sunlife": "80%", "Manulife": "100%", "Equitable": "100%", "Canada Life": "80%", "Benefits by Design": "100%"}, "section": "extendedhealth-class1-salariedees"}, {"key": "paramedicalMaximum", "name": "Paramedical Maximum", "values": {"Sunlife": "$500", "Manulife": "unlimited", "Equitable": "$500", "Canada Life": "$500", "Benefits by Design": "$500"}, "section": "extendedhealth-class1-salariedees"}, {"key": "<PERSON><PERSON><PERSON><PERSON>peechTherapist", "name": "Psychologist/Speech Therapist", "values": {"Sunlife": "$500", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "$500"}, "section": "extendedhealth-class1-salariedees"}, {"key": "visionCare", "name": "Vision", "values": {"Sunlife": "Not Covered", "Manulife": "Not Covered", "Equitable": "$500/24 months", "Canada Life": "$300/24 months", "Benefits by Design": "Not Covered"}, "section": "extendedhealth-class1-salariedees"}, {"key": "eyeExams", "name": "<PERSON>ams", "values": {"Sunlife": "Yes", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "Yes"}, "section": "extendedhealth-class1-salariedees"}, {"key": "privateDutyNursingMaximum", "name": "Private Duty Nursing Maximum", "values": {"Sunlife": "$10,000/year", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "$10,000/year"}, "section": "extendedhealth-class1-salariedees"}, {"key": "semiPrivateHospital", "name": "Semi Private Hospital", "values": {"Sunlife": "Yes", "Manulife": "Yes", "Equitable": "Yes", "Canada Life": "Yes", "Benefits by Design": "Yes"}, "section": "extendedhealth-class1-salariedees"}]}]}, {"sections": [{"id": "extendedhealth-class1-salariedees", "name": "Extended Health -  Class 1 - Salaried EE's", "benefits": [{"key": "hearingAids", "name": "Hearing Aids", "values": {"Sunlife": "$500/5 years", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "$700/5 years"}, "section": "extendedhealth-class1-salariedees"}, {"key": "outOfCountryTravel", "name": "Out of Country Emergency", "values": {"Sunlife": "Yes", "Manulife": "Yes", "Equitable": "Yes", "Canada Life": "Yes", "Benefits by Design": "Yes"}, "section": "extendedhealth-class1-salariedees"}, {"key": "terminationAgeEHC", "name": "Termination Age", "values": {"Sunlife": "Retirement or to age 85", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "Retirement or to age 75"}, "section": "extendedhealth-class1-salariedees"}]}, {"id": "extendedhealth-class2-hourlyees", "name": "Extended Health -  Class 2 - Hourly EE's", "benefits": [{"key": "annualDeductibleEHC", "name": "Annual Deductible", "values": {"Sunlife": "<PERSON>l", "Manulife": "-", "Equitable": "-", "Canada Life": "$25/$50", "Benefits by Design": "<PERSON>l"}, "section": "extendedhealth-class2-hourlyees"}, {"key": "coInsuranceEHC", "name": "CoInsurance", "values": {"Sunlife": "100%", "Manulife": "100%", "Equitable": "100%", "Canada Life": "80%", "Benefits by Design": "100%"}, "section": "extendedhealth-class2-hourlyees"}, {"key": "paramedicalMaximum", "name": "Paramedical Maximum", "values": {"Sunlife": "$500", "Manulife": "-", "Equitable": "-", "Canada Life": "$500", "Benefits by Design": "$500"}, "section": "extendedhealth-class2-hourlyees"}, {"key": "<PERSON><PERSON><PERSON><PERSON>peechTherapist", "name": "Psychologist/Speech Therapist", "values": {"Sunlife": "$500", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "$500"}, "section": "extendedhealth-class2-hourlyees"}, {"key": "visionCare", "name": "Vision", "values": {"Sunlife": "Not Covered", "Manulife": "-", "Equitable": "-", "Canada Life": "$200/24 months", "Benefits by Design": "Not Covered"}, "section": "extendedhealth-class2-hourlyees"}, {"key": "eyeExams", "name": "<PERSON>ams", "values": {"Sunlife": "Yes", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "Yes"}, "section": "extendedhealth-class2-hourlyees"}, {"key": "privateDutyNursingMaximum", "name": "Private Duty Nursing Maximum", "values": {"Sunlife": "$10,000/year", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "$10,000/year"}, "section": "extendedhealth-class2-hourlyees"}]}]}, {"sections": [{"id": "extendedhealth-class2-hourlyees", "name": "Extended Health -  Class 2 - Hourly EE's", "benefits": [{"key": "semiPrivateHospital", "name": "Semi Private Hospital", "values": {"Sunlife": "Yes", "Manulife": "Yes", "Equitable": "-", "Canada Life": "Yes", "Benefits by Design": "Yes"}, "section": "extendedhealth-class2-hourlyees"}, {"key": "hearingAids", "name": "Hearing Aids", "values": {"Sunlife": "$500/5 years", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "$700/5 years"}, "section": "extendedhealth-class2-hourlyees"}, {"key": "outOfCountryTravel", "name": "Out of Country Emergency", "values": {"Sunlife": "Yes", "Manulife": "Yes", "Equitable": "Yes", "Canada Life": "Yes", "Benefits by Design": "Yes"}, "section": "extendedhealth-class2-hourlyees"}, {"key": "terminationAgeEHC", "name": "Termination Age", "values": {"Sunlife": "Retirement or to age 85", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "Retirement or to age 75"}, "section": "extendedhealth-class2-hourlyees"}]}, {"id": "prescriptiondrugs-class1-salariedees", "name": "Prescription Drugs -  Class 1 - Salaried EE's", "benefits": [{"key": "drugDeductible", "name": "Drug Deductible", "values": {"Sunlife": "<PERSON>l", "Manulife": "<PERSON>l", "Equitable": "<PERSON>l", "Canada Life": "<PERSON>l", "Benefits by Design": "<PERSON>l"}, "section": "prescriptiondrugs-class1-salariedees"}, {"key": "prescriptionDrugCoInsurance", "name": "Prescription Drug Co-Insurance", "values": {"Sunlife": "80%", "Manulife": "100%", "Equitable": "100%", "Canada Life": "80%", "Benefits by Design": "80%"}, "section": "prescriptiondrugs-class1-salariedees"}, {"key": "prescriptionMaximum", "name": "Prescription Maximum", "values": {"Sunlife": "unlimited", "Manulife": "unlimited", "Equitable": "unlimited", "Canada Life": "unlimited", "Benefits by Design": "unlimited"}, "section": "prescriptiondrugs-class1-salariedees"}, {"key": "prescriptionDrugType", "name": "Prescription Drug Type", "values": {"Sunlife": "Generic", "Manulife": "National Formulary", "Equitable": "Generic", "Canada Life": "<PERSON><PERSON>", "Benefits by Design": "Generic"}, "section": "prescriptiondrugs-class1-salariedees"}, {"key": "prescriptionPayDirectDrugCard", "name": "Pay Direct Drug Card", "values": {"Sunlife": "Yes", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "Yes"}, "section": "prescriptiondrugs-class1-salariedees"}, {"key": "reimbursement", "name": "Reimbursement Type", "values": {"Sunlife": "Pay Direct", "Manulife": "Pay Direct", "Equitable": "Pay Direct", "Canada Life": "Pay Direct", "Benefits by Design": "Pay Direct"}, "section": "prescriptiondrugs-class1-salariedees"}]}]}, {"sections": [{"id": "prescriptiondrugs-class2-hourlyees", "name": "Prescription Drugs -  Class 2 - Hourly EE's", "benefits": [{"key": "drugDeductible", "name": "Drug Deductible", "values": {"Sunlife": "<PERSON>l", "Manulife": "-", "Equitable": "-", "Canada Life": "<PERSON>l", "Benefits by Design": "<PERSON>l"}, "section": "prescriptiondrugs-class2-hourlyees"}, {"key": "prescriptionDrugCoInsurance", "name": "Prescription Drug Co-Insurance", "values": {"Sunlife": "80%", "Manulife": "-", "Equitable": "-", "Canada Life": "80%", "Benefits by Design": "80%"}, "section": "prescriptiondrugs-class2-hourlyees"}, {"key": "prescriptionMaximum", "name": "Prescription Maximum", "values": {"Sunlife": "unlimited", "Manulife": "-", "Equitable": "-", "Canada Life": "unlimited", "Benefits by Design": "unlimited"}, "section": "prescriptiondrugs-class2-hourlyees"}, {"key": "prescriptionDrugType", "name": "Prescription Drug Type", "values": {"Sunlife": "Generic", "Manulife": "-", "Equitable": "Generic", "Canada Life": "<PERSON><PERSON>", "Benefits by Design": "Generic"}, "section": "prescriptiondrugs-class2-hourlyees"}, {"key": "prescriptionPayDirectDrugCard", "name": "Pay Direct Drug Card", "values": {"Sunlife": "Yes", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "Yes"}, "section": "prescriptiondrugs-class2-hourlyees"}, {"key": "reimbursement", "name": "Reimbursement Type", "values": {"Sunlife": "Pay Direct", "Manulife": "-", "Equitable": "-", "Canada Life": "Pay Direct", "Benefits by Design": "Pay Direct"}, "section": "prescriptiondrugs-class2-hourlyees"}]}, {"id": "dental-class1-salariedees", "name": "Dental -  Class 1 - Salaried EE's", "benefits": [{"key": "annualDeductibleDental", "name": "Annual Deductible", "values": {"Sunlife": "$25/$50", "Manulife": "<PERSON>l", "Equitable": "$25", "Canada Life": "$50", "Benefits by Design": "$25/$50"}, "section": "dental-class1-salariedees"}, {"key": "basicCoInsurance", "name": "Basic Co-Insurance", "values": {"Sunlife": "80%", "Manulife": "100%", "Equitable": "100%", "Canada Life": "80%", "Benefits by Design": "80%"}, "section": "dental-class1-salariedees"}, {"key": "majorCoInsurance", "name": "Major Co-Insurance", "values": {"Sunlife": "50%", "Manulife": "50%", "Equitable": "50%", "Canada Life": "50%", "Benefits by Design": "50%"}, "section": "dental-class1-salariedees"}, {"key": "orthodonticCoInsurance", "name": "Orthodontic Co-Insurance", "values": {"Sunlife": "-", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "-"}, "section": "dental-class1-salariedees"}]}]}, {"sections": [{"id": "dental-class1-salariedees", "name": "Dental -  Class 1 - Salaried EE's", "benefits": [{"key": "basicDentalMaximum", "name": "Basic Dental Maximum", "values": {"Sunlife": "-", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "-"}, "section": "dental-class1-salariedees"}, {"key": "majorDentalMaximum", "name": "Major Dental Maximum", "values": {"Sunlife": "-", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "-"}, "section": "dental-class1-salariedees"}, {"key": "basicAndMajorDentalMaximum", "name": "Basic and Major Dental Maximum", "values": {"Sunlife": "$2,500", "Manulife": "$2,500", "Equitable": "$2,500", "Canada Life": "$2,500", "Benefits by Design": "$2,500"}, "section": "dental-class1-salariedees"}, {"key": "orthodonticLifetimeMaximum", "name": "Orthodontic Lifetime Maximum", "values": {"Sunlife": "-", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "-"}, "section": "dental-class1-salariedees"}, {"key": "dentalRecall", "name": "Dental Recall", "values": {"Sunlife": "6 months", "Manulife": "6 months", "Equitable": "9 months", "Canada Life": "9 months", "Benefits by Design": "2 times per year"}, "section": "dental-class1-salariedees"}, {"key": "terminationAgeDental", "name": "Termination Age", "values": {"Sunlife": "Retirement or to age 85", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "Retirement or to age 75"}, "section": "dental-class1-salariedees"}]}, {"id": "dental-class2-hourlyees", "name": "Dental -  Class 2 - Hourly EE's", "benefits": [{"key": "annualDeductibleDental", "name": "Annual Deductible", "values": {"Sunlife": "$25/$50", "Manulife": "-", "Equitable": "-", "Canada Life": "$25/$50", "Benefits by Design": "$25/$50"}, "section": "dental-class2-hourlyees"}, {"key": "basicCoInsurance", "name": "Basic Co-Insurance", "values": {"Sunlife": "80%", "Manulife": "-", "Equitable": "-", "Canada Life": "80%", "Benefits by Design": "80%"}, "section": "dental-class2-hourlyees"}, {"key": "majorCoInsurance", "name": "Major Co-Insurance", "values": {"Sunlife": "50%", "Manulife": "-", "Equitable": "-", "Canada Life": "50%", "Benefits by Design": "-"}, "section": "dental-class2-hourlyees"}, {"key": "orthodonticCoInsurance", "name": "Orthodontic Co-Insurance", "values": {"Sunlife": "-", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "-"}, "section": "dental-class2-hourlyees"}]}]}, {"sections": [{"id": "dental-class2-hourlyees", "name": "Dental -  Class 2 - Hourly EE's", "benefits": [{"key": "basicDentalMaximum", "name": "Basic Dental Maximum", "values": {"Sunlife": "$1,500", "Manulife": "-", "Equitable": "-", "Canada Life": "$1,500", "Benefits by Design": "$1,500"}, "section": "dental-class2-hourlyees"}, {"key": "majorDentalMaximum", "name": "Major Dental Maximum", "values": {"Sunlife": "-", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "-"}, "section": "dental-class2-hourlyees"}, {"key": "basicAndMajorDentalMaximum", "name": "Basic and Major Dental Maximum", "values": {"Sunlife": "$2,500", "Manulife": "-", "Equitable": "-", "Canada Life": "$1,500", "Benefits by Design": "$1,500"}, "section": "dental-class2-hourlyees"}, {"key": "orthodonticLifetimeMaximum", "name": "Orthodontic Lifetime Maximum", "values": {"Sunlife": "-", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "-"}, "section": "dental-class2-hourlyees"}, {"key": "dentalRecall", "name": "Dental Recall", "values": {"Sunlife": "6 months", "Manulife": "6 months", "Equitable": "-", "Canada Life": "9 months", "Benefits by Design": "2 times per year"}, "section": "dental-class2-hourlyees"}, {"key": "terminationAgeDental", "name": "Termination Age", "values": {"Sunlife": "Retirement or to age 85", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "Retirement or to age 75"}, "section": "dental-class2-hourlyees"}]}, {"id": "longtermdisability-class1-salariedees", "name": "Long Term Disability -  Class 1 - Salaried EE's", "benefits": [{"key": "coverageLTD", "name": "Coverage", "values": {"Sunlife": "66.67%", "Manulife": "66.67%", "Equitable": "66.67%", "Canada Life": "66.67%", "Benefits by Design": "66.67%"}, "section": "longtermdisability-class1-salariedees"}, {"key": "eliminationPeriod", "name": "Elimination Period", "values": {"Sunlife": "120 days", "Manulife": "119 days", "Equitable": "119 days", "Canada Life": "119 days", "Benefits by Design": "119 days"}, "section": "longtermdisability-class1-salariedees"}, {"key": "benefitPeriod", "name": "Benefit Period", "values": {"Sunlife": "to age 65", "Manulife": "to age 65", "Equitable": "to age 65", "Canada Life": "to age 65", "Benefits by Design": "to age 65"}, "section": "longtermdisability-class1-salariedees"}, {"key": "benefitMaximumLTD", "name": "Benefit Maximum", "values": {"Sunlife": "$10,000", "Manulife": "$10,000", "Equitable": "$10,000", "Canada Life": "$10,000", "Benefits by Design": "$10,000"}, "section": "longtermdisability-class1-salariedees"}]}]}, {"sections": [{"id": "longtermdisability-class1-salariedees", "name": "Long Term Disability -  Class 1 - Salaried EE's", "benefits": [{"key": "definitionOfDisability", "name": "Definition of Disability", "values": {"Sunlife": "2 year own occupation", "Manulife": "2 year own occupation", "Equitable": "2 year own occupation", "Canada Life": "2 year own occupation", "Benefits by Design": "2 year own occupation"}, "section": "longtermdisability-class1-salariedees"}, {"key": "taxable", "name": "Taxable", "values": {"Sunlife": "No", "Manulife": "No", "Equitable": "No", "Canada Life": "No", "Benefits by Design": "No"}, "section": "longtermdisability-class1-salariedees"}, {"key": "quoteToNEMOrMax", "name": "Quote to NEM or Max", "values": {"Sunlife": "Max", "Manulife": "Max", "Equitable": "Max", "Canada Life": "Max", "Benefits by Design": "Max"}, "section": "longtermdisability-class1-salariedees"}, {"key": "terminationAgeLTD", "name": "Termination Age", "values": {"Sunlife": "Retirement or to age 65", "Manulife": "Retirement or to age 65", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "Retirement or to age 65"}, "section": "longtermdisability-class1-salariedees"}, {"key": "nonEvidenceMaximum", "name": "nonEvidenceMaximum", "values": {"Sunlife": "$5,500", "Manulife": "$6,200", "Equitable": "$10,000", "Canada Life": "$6,600", "Benefits by Design": "$6,200"}, "section": "longtermdisability-class1-salariedees"}]}, {"id": "longtermdisability-class2-hourlyees", "name": "Long Term Disability -  Class 2 - Hourly EE's", "benefits": [{"key": "coverageLTD", "name": "Coverage", "values": {"Benefits by Design": "66.67%"}, "section": "longtermdisability-class2-hourlyees"}, {"key": "eliminationPeriod", "name": "Elimination Period", "values": {"Benefits by Design": "119 days"}, "section": "longtermdisability-class2-hourlyees"}, {"key": "benefitPeriod", "name": "Benefit Period", "values": {"Benefits by Design": "to age 65"}, "section": "longtermdisability-class2-hourlyees"}, {"key": "benefitMaximumLTD", "name": "Benefit Maximum", "values": {"Benefits by Design": "$10,000"}, "section": "longtermdisability-class2-hourlyees"}, {"key": "definitionOfDisability", "name": "Definition of Disability", "values": {"Benefits by Design": "2 year own occupation"}, "section": "longtermdisability-class2-hourlyees"}]}]}, {"sections": [{"id": "longtermdisability-class2-hourlyees", "name": "Long Term Disability -  Class 2 - Hourly EE's", "benefits": [{"key": "taxable", "name": "Taxable", "values": {"Benefits by Design": "No"}, "section": "longtermdisability-class2-hourlyees"}, {"key": "quoteToNEMOrMax", "name": "Quote to NEM or Max", "values": {"Benefits by Design": "NEM"}, "section": "longtermdisability-class2-hourlyees"}, {"key": "terminationAgeLTD", "name": "Termination Age", "values": {"Benefits by Design": "Retirement or to age 65"}, "section": "longtermdisability-class2-hourlyees"}, {"key": "nonEvidenceMaximum", "name": "nonEvidenceMaximum", "values": {"Benefits by Design": "$6,200"}, "section": "longtermdisability-class2-hourlyees"}]}, {"id": "criticalillness-class1-salariedees", "name": "Critical Illness -  Class 1 - Salaried EE's", "benefits": [{"key": "amountCI", "name": "Amount", "values": {"Manulife": "$50,000"}, "section": "criticalillness-class1-salariedees"}, {"key": "coverageCI", "name": "Coverage", "values": {"Manulife": "Employee Only"}, "section": "criticalillness-class1-salariedees"}, {"key": "terminationAgeCI", "name": "Termination Age", "values": {"Manulife": "Retirement or to age 70"}, "section": "criticalillness-class1-salariedees"}]}, {"id": "criticalillness-class2-hourlyees", "name": "Critical Illness -  Class 2 - Hourly EE's", "benefits": [{"key": "amountCI", "name": "Amount", "values": {"Sunlife": "-", "Manulife": "$50,000"}, "section": "criticalillness-class2-hourlyees"}, {"key": "coverageCI", "name": "Coverage", "values": {"Sunlife": "-", "Manulife": "-"}, "section": "criticalillness-class2-hourlyees"}, {"key": "terminationAgeCI", "name": "Termination Age", "values": {"Sunlife": "-", "Manulife": "Retirement or to age 70"}, "section": "criticalillness-class2-hourlyees"}]}]}, {"sections": [{"id": "employeeassistance-class1-salariedees", "name": "Employee Assistance Program -  Class 1 - Salaried EE's", "benefits": [{"key": "coverageEA", "name": "Coverage", "values": {"Canada Life": "Yes"}, "section": "employeeassistance-class1-salariedees"}]}, {"id": "employeeassistance-class2-hourlyees", "name": "Employee Assistance Program -  Class 2 - Hourly EE's", "benefits": [{"key": "coverageEA", "name": "Coverage", "values": {"Sunlife": "-"}, "section": "employeeassistance-class2-hourlyees"}]}]}]