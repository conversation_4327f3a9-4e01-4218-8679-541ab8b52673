-- Test script for renewal target multi-class function
-- This script helps debug the function and verify it's working correctly

-- Step 1: Deploy the function
\i multi-class/renewal-target.sql

-- Step 2: Verify the function was created successfully
SELECT 
    routine_name,
    routine_type,
    data_type,
    routine_definition IS NOT NULL as has_definition
FROM information_schema.routines 
WHERE routine_name = 'fn_get_renewal_target'
AND routine_schema = 'sandf';

-- Step 3: Check if we have any plans with employee classes and quotes
SELECT 
    p.plan_uuid,
    p.plan_id,
    COUNT(DISTINCT ec.name) as employee_class_count,
    array_agg(DISTINCT ec.name) as employee_classes,
    COUNT(DISTINCT q.quote_id) as quote_count,
    array_agg(DISTINCT c.description) as carriers
FROM sandf.plan p
JOIN sandf.quote q ON q.plan_id = p.plan_id
JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
JOIN sandf.carrier c ON q.carrier_id = c.carrier_id
WHERE ecq.formatted_quote_details IS NOT NULL
AND ecq.formatted_quote_details != '{}'::jsonb
GROUP BY p.plan_uuid, p.plan_id
ORDER BY employee_class_count DESC, quote_count DESC
LIMIT 5;

-- Step 4: Test with a specific plan (replace with your actual plan UUID)
-- Uncomment and modify the following lines to test:

-- Test with debugging enabled
-- SET client_min_messages = NOTICE;

-- SELECT sandf.fn_get_renewal_target(
--     'your-plan-uuid-here',
--     'your-user-id-here'
-- );

-- Step 5: Check for any plans that might have the old structure
SELECT 
    p.plan_uuid,
    ec.name as employee_class,
    q.quote_id,
    c.description as carrier,
    CASE 
        WHEN ecq.formatted_quote_details #> '{benefitPremiums,extendedHealthPremium}' ? 'single' THEN 'nested_structure'
        WHEN ecq.formatted_quote_details #> '{benefitPremiums,extendedHealthPremium}' IS NOT NULL THEN 'direct_structure'
        ELSE 'no_ehc_data'
    END as ehc_structure,
    CASE 
        WHEN ecq.formatted_quote_details #> '{benefitPremiums,dentalCarePremium}' ? 'single' THEN 'nested_structure'
        WHEN ecq.formatted_quote_details #> '{benefitPremiums,dentalCarePremium}' IS NOT NULL THEN 'direct_structure'
        ELSE 'no_dental_data'
    END as dental_structure,
    CASE 
        WHEN ecq.formatted_quote_details #> '{benefitPremiums,ratingFactors}' IS NOT NULL THEN 'has_rating_factors'
        ELSE 'no_rating_factors'
    END as rating_factors_status
FROM sandf.plan p
JOIN sandf.quote q ON q.plan_id = p.plan_id
JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
JOIN sandf.carrier c ON q.carrier_id = c.carrier_id
WHERE ecq.formatted_quote_details IS NOT NULL
AND ecq.formatted_quote_details != '{}'::jsonb
ORDER BY p.plan_uuid, ec.name, c.description
LIMIT 10;

-- Step 6: Sample data structure check
-- This shows what the function expects to find
SELECT 
    p.plan_uuid,
    ec.name as employee_class,
    c.description as carrier,
    ecq.formatted_quote_details #> '{benefitPremiums,extendedHealthPremium}' as ehc_premium_data,
    ecq.formatted_quote_details #> '{benefitPremiums,dentalCarePremium}' as dental_premium_data,
    ecq.formatted_quote_details #> '{benefitPremiums,ratingFactors}' as rating_factors_data
FROM sandf.plan p
JOIN sandf.quote q ON q.plan_id = p.plan_id
JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
JOIN sandf.carrier c ON q.carrier_id = c.carrier_id
WHERE ecq.formatted_quote_details IS NOT NULL
AND ecq.formatted_quote_details != '{}'::jsonb
AND p.plan_uuid = 'your-plan-uuid-here'  -- Replace with actual UUID
ORDER BY ec.name, c.description;
