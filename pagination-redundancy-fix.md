# Pagination Redundancy Fix

## Problem Identified

The `plan-design-multi-class.sql` function was creating paginated output where every page object included the full `carriers` array, causing redundancy in the JSON structure:

```json
[
  {
    "carriers": ["Equitable", "Canada Life", "Benefits by Design", "Manulife", "Sunlife"],
    "sections": [...]
  },
  {
    "carriers": ["Equitable", "Canada Life", "Benefits by Design", "Manulife", "Sunlife"], // REDUNDANT
    "sections": [...]
  },
  // ... more pages with duplicate carriers
]
```

## Solution Implemented

Modified the pagination logic to include the `carriers` array only in the **first page object**, while subsequent pages contain only the `sections` array.

### Changes Made

#### File: `multi-class/function-plan-design/plan-design-multi-class.sql`

1. **Added new variables** (lines 73-74):
   ```sql
   is_first_page BOOLEAN;
   page_object JSONB;
   ```

2. **Initialize first page flag** (line 456):
   ```sql
   is_first_page := TRUE;
   ```

3. **Updated pagination logic** (lines 496-509):
   ```sql
   -- Build page object - include carriers only on first page
   IF is_first_page THEN
       page_object := jsonb_build_object(
           'carriers', ordered_carriers_array,
           'sections', current_page_sections
       );
       is_first_page := FALSE;
   ELSE
       page_object := jsonb_build_object(
           'sections', current_page_sections
       );
   END IF;
   
   result_pages := result_pages || jsonb_build_array(page_object);
   ```

4. **Updated final page logic** (lines 533-545):
   ```sql
   -- Build final page object - include carriers only if this is the first page
   IF is_first_page THEN
       page_object := jsonb_build_object(
           'carriers', ordered_carriers_array,
           'sections', current_page_sections
       );
   ELSE
       page_object := jsonb_build_object(
           'sections', current_page_sections
       );
   END IF;
   ```

## Expected Results

### Before Fix:
```json
[
  {
    "carriers": [...],  // 5 carriers
    "sections": [...]
  },
  {
    "carriers": [...],  // Same 5 carriers (redundant)
    "sections": [...]
  }
]
```

### After Fix:
```json
[
  {
    "carriers": [...],  // 5 carriers (only in first page)
    "sections": [...]
  },
  {
    "sections": [...]   // No carriers array (removes redundancy)
  }
]
```

## Benefits

1. **Reduced JSON Size**: Eliminates duplicate carrier arrays across pages
2. **Improved Performance**: Less data transfer and parsing overhead
3. **Cleaner Structure**: Logical separation where carriers are defined once
4. **Maintained Functionality**: Section IDs remain unique per page object
5. **Backward Compatibility**: First page still contains carriers for existing consumers

## Testing

Use the provided `test-pagination-fix.sql` script to verify:
- Carriers array appears only in the first page
- All subsequent pages contain only sections
- Section IDs remain unique across pages
- Total section count is preserved

## Impact

This fix addresses the redundancy issue identified in the t2.json file while maintaining the intentional pagination structure for large datasets.
