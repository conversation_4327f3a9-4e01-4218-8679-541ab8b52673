[{"carriers": ["Equitable", "Canada Life", "Benefits by Design", "Manulife", "Sunlife"], "sections": [{"id": "criticalillness1class1-salar<PERSON><PERSON>'s", "name": "Critical Illness - 1 Class 1 - Salaried EE's", "benefits": [{"key": "amountCI", "name": "Amount", "values": {"Manulife": "$50,000"}, "section": "criticalIllness1 Class 1 - Salaried EE's"}, {"key": "coverageCI", "name": "Coverage", "values": {"Manulife": "Employee Only"}, "section": "criticalIllness1 Class 1 - Salaried EE's"}, {"key": "terminationAgeCI", "name": "Termination Age", "values": {"Manulife": "Retirement or to age 70"}, "section": "criticalIllness1 Class 1 - Salaried EE's"}]}, {"id": "criticalillness2class2-<PERSON><PERSON>'s", "name": "Critical Illness - 2 Class 2 - Hourly EE's", "benefits": [{"key": "amountCI", "name": "Amount", "values": {"Sunlife": "-", "Manulife": "$50,000"}, "section": "criticalIllness2 Class 2 - Hourly EE's"}, {"key": "coverageCI", "name": "Coverage", "values": {"Sunlife": "-", "Manulife": "-"}, "section": "criticalIllness2 Class 2 - Hourly EE's"}, {"key": "terminationAgeCI", "name": "Termination Age", "values": {"Sunlife": "-", "Manulife": "Retirement or to age 70"}, "section": "criticalIllness2 Class 2 - Hourly EE's"}]}, {"id": "dental1class1-salar<PERSON><PERSON>'s", "name": "Dental - 1 Class 1 - Salaried EE's", "benefits": [{"key": "annualDeductibleDental", "name": "Annual Deductible", "values": {"Sunlife": "$25/$50", "Manulife": "<PERSON>l", "Equitable": "$25", "Canada Life": "$50", "Benefits by Design": "$25/$50"}, "section": "dental1 Class 1 - Salaried EE's"}, {"key": "basicCoInsurance", "name": "Basic Co-Insurance", "values": {"Sunlife": "80%", "Manulife": "100%", "Equitable": "100%", "Canada Life": "80%", "Benefits by Design": "80%"}, "section": "dental1 Class 1 - Salaried EE's"}, {"key": "majorCoInsurance", "name": "Major Co-Insurance", "values": {"Sunlife": "50%", "Manulife": "50%", "Equitable": "50%", "Canada Life": "50%", "Benefits by Design": "50%"}, "section": "dental1 Class 1 - Salaried EE's"}, {"key": "orthodonticCoInsurance", "name": "Orthodontic Co-Insurance", "values": {"Sunlife": "-", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "-"}, "section": "dental1 Class 1 - Salaried EE's"}]}]}, {"carriers": ["Equitable", "Canada Life", "Benefits by Design", "Manulife", "Sunlife"], "sections": [{"id": "dental1class1-salar<PERSON><PERSON>'s", "name": "Dental - 1 Class 1 - Salaried EE's", "benefits": [{"key": "basicDentalMaximum", "name": "Basic Dental Maximum", "values": {"Sunlife": "-", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "-"}, "section": "dental1 Class 1 - Salaried EE's"}, {"key": "majorDentalMaximum", "name": "Major Dental Maximum", "values": {"Sunlife": "-", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "-"}, "section": "dental1 Class 1 - Salaried EE's"}, {"key": "basicAndMajorDentalMaximum", "name": "Basic and Major Dental Maximum", "values": {"Sunlife": "$2,500", "Manulife": "$2,500", "Equitable": "$2,500", "Canada Life": "$2,500", "Benefits by Design": "$2,500"}, "section": "dental1 Class 1 - Salaried EE's"}, {"key": "orthodonticLifetimeMaximum", "name": "Orthodontic Lifetime Maximum", "values": {"Sunlife": "-", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "-"}, "section": "dental1 Class 1 - Salaried EE's"}, {"key": "dentalRecall", "name": "Dental Recall", "values": {"Sunlife": "6 months", "Manulife": "6 months", "Equitable": "9 months", "Canada Life": "9 months", "Benefits by Design": "2 times per year"}, "section": "dental1 Class 1 - Salaried EE's"}, {"key": "terminationAgeDental", "name": "Termination Age", "values": {"Sunlife": "Retirement or to age 85", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "Retirement or to age 75"}, "section": "dental1 Class 1 - Salaried EE's"}]}, {"id": "dental2class2-<PERSON><PERSON>'s", "name": "Dental - 2 Class 2 - Hourly EE's", "benefits": [{"key": "annualDeductibleDental", "name": "Annual Deductible", "values": {"Sunlife": "$25/$50", "Manulife": "-", "Equitable": "-", "Canada Life": "$25/$50", "Benefits by Design": "$25/$50"}, "section": "dental2 Class 2 - Hourly EE's"}, {"key": "basicCoInsurance", "name": "Basic Co-Insurance", "values": {"Sunlife": "80%", "Manulife": "-", "Equitable": "-", "Canada Life": "80%", "Benefits by Design": "80%"}, "section": "dental2 Class 2 - Hourly EE's"}, {"key": "majorCoInsurance", "name": "Major Co-Insurance", "values": {"Sunlife": "50%", "Manulife": "-", "Equitable": "-", "Canada Life": "50%", "Benefits by Design": "-"}, "section": "dental2 Class 2 - Hourly EE's"}, {"key": "orthodonticCoInsurance", "name": "Orthodontic Co-Insurance", "values": {"Sunlife": "-", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "-"}, "section": "dental2 Class 2 - Hourly EE's"}]}]}, {"carriers": ["Equitable", "Canada Life", "Benefits by Design", "Manulife", "Sunlife"], "sections": [{"id": "dental2class2-<PERSON><PERSON>'s", "name": "Dental - 2 Class 2 - Hourly EE's", "benefits": [{"key": "basicDentalMaximum", "name": "Basic Dental Maximum", "values": {"Sunlife": "$1,500", "Manulife": "-", "Equitable": "-", "Canada Life": "$1,500", "Benefits by Design": "$1,500"}, "section": "dental2 Class 2 - Hourly EE's"}, {"key": "majorDentalMaximum", "name": "Major Dental Maximum", "values": {"Sunlife": "-", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "-"}, "section": "dental2 Class 2 - Hourly EE's"}, {"key": "basicAndMajorDentalMaximum", "name": "Basic and Major Dental Maximum", "values": {"Sunlife": "$2,500", "Manulife": "-", "Equitable": "-", "Canada Life": "$1,500", "Benefits by Design": "$1,500"}, "section": "dental2 Class 2 - Hourly EE's"}, {"key": "orthodonticLifetimeMaximum", "name": "Orthodontic Lifetime Maximum", "values": {"Sunlife": "-", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "-"}, "section": "dental2 Class 2 - Hourly EE's"}, {"key": "dentalRecall", "name": "Dental Recall", "values": {"Sunlife": "6 months", "Manulife": "6 months", "Equitable": "-", "Canada Life": "9 months", "Benefits by Design": "2 times per year"}, "section": "dental2 Class 2 - Hourly EE's"}, {"key": "terminationAgeDental", "name": "Termination Age", "values": {"Sunlife": "Retirement or to age 85", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "Retirement or to age 75"}, "section": "dental2 Class 2 - Hourly EE's"}]}, {"id": "dependentlife1class1-salar<PERSON><PERSON>'s", "name": "Dependent Life - 1 Class 1 - Salaried EE's", "benefits": [{"key": "spouse", "name": "Spouse", "values": {"Canada Life": "-"}, "section": "dependentLife1 Class 1 - Salaried EE's"}, {"key": "child", "name": "Child", "values": {"Canada Life": "-"}, "section": "dependentLife1 Class 1 - Salaried EE's"}]}, {"id": "dependentlife2class2-hourlyee's", "name": "Dependent Life - 2 Class 2 - Hourly EE's", "benefits": [{"key": "spouse", "name": "Spouse", "values": {"Equitable": "-", "Canada Life": "-"}, "section": "dependentLife2 Class 2 - Hourly EE's"}, {"key": "child", "name": "Child", "values": {"Equitable": "-", "Canada Life": "-"}, "section": "dependentLife2 Class 2 - Hourly EE's"}]}]}, {"carriers": ["Equitable", "Canada Life", "Benefits by Design", "Manulife", "Sunlife"], "sections": [{"id": "employeeassistance1class1-salaried<PERSON>'s", "name": "Employee Assistance Program - 1 Class 1 - Salaried EE's", "benefits": [{"key": "coverageEA", "name": "Coverage", "values": {"Canada Life": "Yes"}, "section": "employeeAssistance1 Class 1 - Salaried EE's"}]}, {"id": "employeeassistance2class2-hourlyee's", "name": "Employee Assistance Program - 2 Class 2 - Hourly EE's", "benefits": [{"key": "coverageEA", "name": "Coverage", "values": {"Sunlife": "-"}, "section": "employeeAssistance2 Class 2 - Hourly EE's"}]}, {"id": "extendedhealth1class1-salar<PERSON><PERSON>'s", "name": "Extended Health - 1 Class 1 - Salaried EE's", "benefits": [{"key": "annualDeductibleEHC", "name": "Annual Deductible", "values": {"Sunlife": "<PERSON>l", "Manulife": "<PERSON>l", "Equitable": "$25", "Canada Life": "$50", "Benefits by Design": "<PERSON>l"}, "section": "extendedHealth1 Class 1 - Salaried EE's"}, {"key": "coInsuranceEHC", "name": "CoInsurance", "values": {"Sunlife": "80%", "Manulife": "100%", "Equitable": "100%", "Canada Life": "80%", "Benefits by Design": "100%"}, "section": "extendedHealth1 Class 1 - Salaried EE's"}, {"key": "paramedicalMaximum", "name": "Paramedical Maximum", "values": {"Sunlife": "$500", "Manulife": "unlimited", "Equitable": "$500", "Canada Life": "$500", "Benefits by Design": "$500"}, "section": "extendedHealth1 Class 1 - Salaried EE's"}, {"key": "<PERSON><PERSON><PERSON><PERSON>peechTherapist", "name": "Psychologist/Speech Therapist", "values": {"Sunlife": "$500", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "$500"}, "section": "extendedHealth1 Class 1 - Salaried EE's"}, {"key": "visionCare", "name": "Vision", "values": {"Sunlife": "Not Covered", "Manulife": "Not Covered", "Equitable": "$500/24 months", "Canada Life": "$300/24 months", "Benefits by Design": "Not Covered"}, "section": "extendedHealth1 Class 1 - Salaried EE's"}, {"key": "eyeExams", "name": "<PERSON>ams", "values": {"Sunlife": "Yes", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "Yes"}, "section": "extendedHealth1 Class 1 - Salaried EE's"}, {"key": "privateDutyNursingMaximum", "name": "Private Duty Nursing Maximum", "values": {"Sunlife": "$10,000/year", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "$10,000/year"}, "section": "extendedHealth1 Class 1 - Salaried EE's"}, {"key": "semiPrivateHospital", "name": "Semi Private Hospital", "values": {"Sunlife": "Yes", "Manulife": "Yes", "Equitable": "Yes", "Canada Life": "Yes", "Benefits by Design": "Yes"}, "section": "extendedHealth1 Class 1 - Salaried EE's"}]}]}, {"carriers": ["Equitable", "Canada Life", "Benefits by Design", "Manulife", "Sunlife"], "sections": [{"id": "extendedhealth1class1-salar<PERSON><PERSON>'s", "name": "Extended Health - 1 Class 1 - Salaried EE's", "benefits": [{"key": "hearingAids", "name": "Hearing Aids", "values": {"Sunlife": "$500/5 years", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "$700/5 years"}, "section": "extendedHealth1 Class 1 - Salaried EE's"}, {"key": "outOfCountryTravel", "name": "Out of Country Emergency", "values": {"Sunlife": "Yes", "Manulife": "Yes", "Equitable": "Yes", "Canada Life": "Yes", "Benefits by Design": "Yes"}, "section": "extendedHealth1 Class 1 - Salaried EE's"}, {"key": "terminationAgeEHC", "name": "Termination Age", "values": {"Sunlife": "Retirement or to age 85", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "Retirement or to age 75"}, "section": "extendedHealth1 Class 1 - Salaried EE's"}]}, {"id": "extendedhealth2class2-<PERSON>ee's", "name": "Extended Health - 2 Class 2 - Hourly EE's", "benefits": [{"key": "annualDeductibleEHC", "name": "Annual Deductible", "values": {"Sunlife": "<PERSON>l", "Manulife": "-", "Equitable": "-", "Canada Life": "$25/$50", "Benefits by Design": "<PERSON>l"}, "section": "extendedHealth2 Class 2 - Hourly EE's"}, {"key": "coInsuranceEHC", "name": "CoInsurance", "values": {"Sunlife": "100%", "Manulife": "100%", "Equitable": "100%", "Canada Life": "80%", "Benefits by Design": "100%"}, "section": "extendedHealth2 Class 2 - Hourly EE's"}, {"key": "paramedicalMaximum", "name": "Paramedical Maximum", "values": {"Sunlife": "$500", "Manulife": "-", "Equitable": "-", "Canada Life": "$500", "Benefits by Design": "$500"}, "section": "extendedHealth2 Class 2 - Hourly EE's"}, {"key": "<PERSON><PERSON><PERSON><PERSON>peechTherapist", "name": "Psychologist/Speech Therapist", "values": {"Sunlife": "$500", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "$500"}, "section": "extendedHealth2 Class 2 - Hourly EE's"}, {"key": "visionCare", "name": "Vision", "values": {"Sunlife": "Not Covered", "Manulife": "-", "Equitable": "-", "Canada Life": "$200/24 months", "Benefits by Design": "Not Covered"}, "section": "extendedHealth2 Class 2 - Hourly EE's"}, {"key": "eyeExams", "name": "<PERSON>ams", "values": {"Sunlife": "Yes", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "Yes"}, "section": "extendedHealth2 Class 2 - Hourly EE's"}, {"key": "privateDutyNursingMaximum", "name": "Private Duty Nursing Maximum", "values": {"Sunlife": "$10,000/year", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "$10,000/year"}, "section": "extendedHealth2 Class 2 - Hourly EE's"}]}]}, {"carriers": ["Equitable", "Canada Life", "Benefits by Design", "Manulife", "Sunlife"], "sections": [{"id": "extendedhealth2class2-<PERSON>ee's", "name": "Extended Health - 2 Class 2 - Hourly EE's", "benefits": [{"key": "semiPrivateHospital", "name": "Semi Private Hospital", "values": {"Sunlife": "Yes", "Manulife": "Yes", "Equitable": "-", "Canada Life": "Yes", "Benefits by Design": "Yes"}, "section": "extendedHealth2 Class 2 - Hourly EE's"}, {"key": "hearingAids", "name": "Hearing Aids", "values": {"Sunlife": "$500/5 years", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "$700/5 years"}, "section": "extendedHealth2 Class 2 - Hourly EE's"}, {"key": "outOfCountryTravel", "name": "Out of Country Emergency", "values": {"Sunlife": "Yes", "Manulife": "Yes", "Equitable": "Yes", "Canada Life": "Yes", "Benefits by Design": "Yes"}, "section": "extendedHealth2 Class 2 - Hourly EE's"}, {"key": "terminationAgeEHC", "name": "Termination Age", "values": {"Sunlife": "Retirement or to age 85", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "Retirement or to age 75"}, "section": "extendedHealth2 Class 2 - Hourly EE's"}]}, {"id": "lifeinsuranceadad1class1-salaried<PERSON>'s", "name": "Life Insurance & AD&D - 1 Class 1 - Salaried EE's", "benefits": [{"key": "coverageLife", "name": "Amount of Coverage", "values": {"Sunlife": "$25,000", "Manulife": "$25,000", "Equitable": "2 times annual earnings", "Canada Life": "2 times annual earnings", "Benefits by Design": "$25,000"}, "section": "lifeInsuranceADAD1 Class 1 - Salaried EE's"}, {"key": "ageReduction", "name": "Age Reduction", "values": {"Sunlife": "50% at age 65", "Manulife": "50% at age 65", "Equitable": "70", "Canada Life": "50% at age 65", "Benefits by Design": "50% at age 65, $25,000 age 70"}, "section": "lifeInsuranceADAD1 Class 1 - Salaried EE's"}, {"key": "terminationAgeLife", "name": "Termination Age - Life", "values": {"Sunlife": "Retirement or to age 70", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "Retirement or to age 75"}, "section": "lifeInsuranceADAD1 Class 1 - Salaried EE's"}, {"key": "terminationAgeADAD", "name": "Termination Age - AD&D", "values": {"Sunlife": "Retirement or to age 70", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "Retirement or to age 75"}, "section": "lifeInsuranceADAD1 Class 1 - Salaried EE's"}]}, {"id": "lifeinsuranceadad2class2-hourlyee's", "name": "Life Insurance & AD&D - 2 Class 2 - Hourly EE's", "benefits": [{"key": "coverageLife", "name": "Amount of Coverage", "values": {"Sunlife": "$25,000", "Manulife": "$25,000", "Equitable": "$25,000", "Canada Life": "2 times annual earnings", "Benefits by Design": "$25,000"}, "section": "lifeInsuranceADAD2 Class 2 - Hourly EE's"}, {"key": "ageReduction", "name": "Age Reduction", "values": {"Sunlife": "50% at age 65", "Manulife": "50% at age 65", "Equitable": "50% at age 65", "Canada Life": "65% at age 65, 50% at age 70", "Benefits by Design": "50% at age 65, $25,000 age 70"}, "section": "lifeInsuranceADAD2 Class 2 - Hourly EE's"}]}]}, {"carriers": ["Equitable", "Canada Life", "Benefits by Design", "Manulife", "Sunlife"], "sections": [{"id": "lifeinsuranceadad2class2-hourlyee's", "name": "Life Insurance & AD&D - 2 Class 2 - Hourly EE's", "benefits": [{"key": "terminationAgeLife", "name": "Termination Age - Life", "values": {"Sunlife": "Retirement or to age 70", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "Retirement or to age 75"}, "section": "lifeInsuranceADAD2 Class 2 - Hourly EE's"}, {"key": "terminationAgeADAD", "name": "Termination Age - AD&D", "values": {"Sunlife": "Retirement or to age 70", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "Retirement or to age 75"}, "section": "lifeInsuranceADAD2 Class 2 - Hourly EE's"}]}, {"id": "longtermdisability1class1-salaried<PERSON>'s", "name": "Long Term Disability - 1 Class 1 - Salaried EE's", "benefits": [{"key": "coverageLTD", "name": "Coverage", "values": {"Sunlife": "66.67%", "Manulife": "66.67%", "Equitable": "66.67%", "Canada Life": "66.67%", "Benefits by Design": "66.67%"}, "section": "longTermDisability1 Class 1 - Salaried EE's"}, {"key": "eliminationPeriod", "name": "Elimination Period", "values": {"Sunlife": "120 days", "Manulife": "119 days", "Equitable": "119 days", "Canada Life": "119 days", "Benefits by Design": "119 days"}, "section": "longTermDisability1 Class 1 - Salaried EE's"}, {"key": "benefitPeriod", "name": "Benefit Period", "values": {"Sunlife": "to age 65", "Manulife": "to age 65", "Equitable": "to age 65", "Canada Life": "to age 65", "Benefits by Design": "to age 65"}, "section": "longTermDisability1 Class 1 - Salaried EE's"}, {"key": "benefitMaximumLTD", "name": "Benefit Maximum", "values": {"Sunlife": "$10,000", "Manulife": "$10,000", "Equitable": "$10,000", "Canada Life": "$10,000", "Benefits by Design": "$10,000"}, "section": "longTermDisability1 Class 1 - Salaried EE's"}, {"key": "definitionOfDisability", "name": "Definition of Disability", "values": {"Sunlife": "2 year own occupation", "Manulife": "2 year own occupation", "Equitable": "2 year own occupation", "Canada Life": "2 year own occupation", "Benefits by Design": "2 year own occupation"}, "section": "longTermDisability1 Class 1 - Salaried EE's"}, {"key": "taxable", "name": "Taxable", "values": {"Sunlife": "No", "Manulife": "No", "Equitable": "No", "Canada Life": "No", "Benefits by Design": "No"}, "section": "longTermDisability1 Class 1 - Salaried EE's"}, {"key": "quoteToNEMOrMax", "name": "Quote to NEM or Max", "values": {"Sunlife": "Max", "Manulife": "Max", "Equitable": "Max", "Canada Life": "Max", "Benefits by Design": "Max"}, "section": "longTermDisability1 Class 1 - Salaried EE's"}, {"key": "terminationAgeLTD", "name": "Termination Age", "values": {"Sunlife": "Retirement or to age 65", "Manulife": "Retirement or to age 65", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "Retirement or to age 65"}, "section": "longTermDisability1 Class 1 - Salaried EE's"}]}]}, {"carriers": ["Equitable", "Canada Life", "Benefits by Design", "Manulife", "Sunlife"], "sections": [{"id": "longtermdisability1class1-salaried<PERSON>'s", "name": "Long Term Disability - 1 Class 1 - Salaried EE's", "benefits": [{"key": "nonEvidenceMaximum", "name": "nonEvidenceMaximum", "values": {"Sunlife": "$5,500", "Manulife": "$6,200", "Equitable": "$10,000", "Canada Life": "$6,600", "Benefits by Design": "$6,200"}, "section": "longTermDisability1 Class 1 - Salaried EE's"}]}, {"id": "longtermdisability2class2-hourlyee's", "name": "Long Term Disability - 2 Class 2 - Hourly EE's", "benefits": [{"key": "coverageLTD", "name": "Coverage", "values": {"Benefits by Design": "66.67%"}, "section": "longTermDisability2 Class 2 - Hourly EE's"}, {"key": "eliminationPeriod", "name": "Elimination Period", "values": {"Benefits by Design": "119 days"}, "section": "longTermDisability2 Class 2 - Hourly EE's"}, {"key": "benefitPeriod", "name": "Benefit Period", "values": {"Benefits by Design": "to age 65"}, "section": "longTermDisability2 Class 2 - Hourly EE's"}, {"key": "benefitMaximumLTD", "name": "Benefit Maximum", "values": {"Benefits by Design": "$10,000"}, "section": "longTermDisability2 Class 2 - Hourly EE's"}, {"key": "definitionOfDisability", "name": "Definition of Disability", "values": {"Benefits by Design": "2 year own occupation"}, "section": "longTermDisability2 Class 2 - Hourly EE's"}, {"key": "taxable", "name": "Taxable", "values": {"Benefits by Design": "No"}, "section": "longTermDisability2 Class 2 - Hourly EE's"}, {"key": "quoteToNEMOrMax", "name": "Quote to NEM or Max", "values": {"Benefits by Design": "NEM"}, "section": "longTermDisability2 Class 2 - Hourly EE's"}, {"key": "terminationAgeLTD", "name": "Termination Age", "values": {"Benefits by Design": "Retirement or to age 65"}, "section": "longTermDisability2 Class 2 - Hourly EE's"}, {"key": "nonEvidenceMaximum", "name": "nonEvidenceMaximum", "values": {"Benefits by Design": "$6,200"}, "section": "longTermDisability2 Class 2 - Hourly EE's"}]}]}, {"carriers": ["Equitable", "Canada Life", "Benefits by Design", "Manulife", "Sunlife"], "sections": [{"id": "prescriptiondrugs1class1-salaried<PERSON>'s", "name": "Prescription Drugs - 1 Class 1 - Salaried EE's", "benefits": [{"key": "drugDeductible", "name": "Drug Deductible", "values": {"Sunlife": "<PERSON>l", "Manulife": "<PERSON>l", "Equitable": "<PERSON>l", "Canada Life": "<PERSON>l", "Benefits by Design": "<PERSON>l"}, "section": "prescriptionDrugs1 Class 1 - Salaried EE's"}, {"key": "prescriptionDrugCoInsurance", "name": "Prescription Drug Co-Insurance", "values": {"Sunlife": "80%", "Manulife": "100%", "Equitable": "100%", "Canada Life": "80%", "Benefits by Design": "80%"}, "section": "prescriptionDrugs1 Class 1 - Salaried EE's"}, {"key": "prescriptionMaximum", "name": "Prescription Maximum", "values": {"Sunlife": "unlimited", "Manulife": "unlimited", "Equitable": "unlimited", "Canada Life": "unlimited", "Benefits by Design": "unlimited"}, "section": "prescriptionDrugs1 Class 1 - Salaried EE's"}, {"key": "prescriptionDrugType", "name": "Prescription Drug Type", "values": {"Sunlife": "Generic", "Manulife": "National Formulary", "Equitable": "Generic", "Canada Life": "<PERSON><PERSON>", "Benefits by Design": "Generic"}, "section": "prescriptionDrugs1 Class 1 - Salaried EE's"}, {"key": "prescriptionPayDirectDrugCard", "name": "Pay Direct Drug Card", "values": {"Sunlife": "Yes", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "Yes"}, "section": "prescriptionDrugs1 Class 1 - Salaried EE's"}, {"key": "reimbursement", "name": "Reimbursement Type", "values": {"Sunlife": "Pay Direct", "Manulife": "Pay Direct", "Equitable": "Pay Direct", "Canada Life": "Pay Direct", "Benefits by Design": "Pay Direct"}, "section": "prescriptionDrugs1 Class 1 - Salaried EE's"}]}, {"id": "prescriptiondrugs2class2-hourlyee's", "name": "Prescription Drugs - 2 Class 2 - Hourly EE's", "benefits": [{"key": "drugDeductible", "name": "Drug Deductible", "values": {"Sunlife": "<PERSON>l", "Manulife": "-", "Equitable": "-", "Canada Life": "<PERSON>l", "Benefits by Design": "<PERSON>l"}, "section": "prescriptionDrugs2 Class 2 - Hourly EE's"}, {"key": "prescriptionDrugCoInsurance", "name": "Prescription Drug Co-Insurance", "values": {"Sunlife": "80%", "Manulife": "-", "Equitable": "-", "Canada Life": "80%", "Benefits by Design": "80%"}, "section": "prescriptionDrugs2 Class 2 - Hourly EE's"}, {"key": "prescriptionMaximum", "name": "Prescription Maximum", "values": {"Sunlife": "unlimited", "Manulife": "-", "Equitable": "-", "Canada Life": "unlimited", "Benefits by Design": "unlimited"}, "section": "prescriptionDrugs2 Class 2 - Hourly EE's"}, {"key": "prescriptionDrugType", "name": "Prescription Drug Type", "values": {"Sunlife": "Generic", "Manulife": "-", "Equitable": "Generic", "Canada Life": "<PERSON><PERSON>", "Benefits by Design": "Generic"}, "section": "prescriptionDrugs2 Class 2 - Hourly EE's"}]}]}, {"carriers": ["Equitable", "Canada Life", "Benefits by Design", "Manulife", "Sunlife"], "sections": [{"id": "prescriptiondrugs2class2-hourlyee's", "name": "Prescription Drugs - 2 Class 2 - Hourly EE's", "benefits": [{"key": "prescriptionPayDirectDrugCard", "name": "Pay Direct Drug Card", "values": {"Sunlife": "Yes", "Manulife": "-", "Equitable": "-", "Canada Life": "-", "Benefits by Design": "Yes"}, "section": "prescriptionDrugs2 Class 2 - Hourly EE's"}, {"key": "reimbursement", "name": "Reimbursement Type", "values": {"Sunlife": "Pay Direct", "Manulife": "-", "Equitable": "-", "Canada Life": "Pay Direct", "Benefits by Design": "Pay Direct"}, "section": "prescriptionDrugs2 Class 2 - Hourly EE's"}]}]}]