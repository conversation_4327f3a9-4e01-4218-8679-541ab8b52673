-- Test script to verify the pagination fix for removing redundant carrier arrays
-- This tests that carriers array only appears in the first page object

-- Test the updated multi-class plan design function
SELECT 
    'Testing pagination fix...' as test_status;

-- You can test with your actual plan UUID
-- SELECT sandf.fn_get_plan_design_report_multi_class(
--     'your-plan-uuid-here'::uuid,
--     'your-user-id-here'::uuid,
--     NULL, -- includes
--     NULL  -- excludes
-- );

-- Test script to check the structure of the returned JSON
DO $$
DECLARE
    test_result TEXT;
    parsed_result JSONB;
    page_count INTEGER;
    page_index INTEGER;
    page_obj JSONB;
    has_carriers BOOLEAN;
    carriers_count INTEGER := 0;
    sections_count INTEGER := 0;
BEGIN
    -- This would be your actual function call
    -- test_result := sandf.fn_get_plan_design_report_multi_class(
    --     'your-plan-uuid-here'::uuid,
    --     'your-user-id-here'::uuid,
    --     NULL,
    --     NULL
    -- );
    
    -- For testing purposes, simulate the expected structure
    test_result := '[
        {
            "carriers": ["Carrier1", "Carrier2"],
            "sections": [{"id": "section1", "name": "Section 1", "benefits": []}]
        },
        {
            "sections": [{"id": "section2", "name": "Section 2", "benefits": []}]
        }
    ]';
    
    parsed_result := test_result::JSONB;
    page_count := jsonb_array_length(parsed_result);
    
    RAISE NOTICE 'Total pages: %', page_count;
    
    FOR page_index IN 0..page_count-1 LOOP
        page_obj := parsed_result -> page_index;
        has_carriers := page_obj ? 'carriers';
        
        IF has_carriers THEN
            carriers_count := carriers_count + 1;
            RAISE NOTICE 'Page % has carriers array with % carriers', 
                page_index + 1, 
                jsonb_array_length(page_obj -> 'carriers');
        ELSE
            RAISE NOTICE 'Page % has no carriers array', page_index + 1;
        END IF;
        
        IF page_obj ? 'sections' THEN
            sections_count := sections_count + jsonb_array_length(page_obj -> 'sections');
            RAISE NOTICE 'Page % has % sections', 
                page_index + 1, 
                jsonb_array_length(page_obj -> 'sections');
        END IF;
    END LOOP;
    
    RAISE NOTICE '---';
    RAISE NOTICE 'Summary:';
    RAISE NOTICE 'Pages with carriers array: %', carriers_count;
    RAISE NOTICE 'Total sections across all pages: %', sections_count;
    
    IF carriers_count = 1 THEN
        RAISE NOTICE 'SUCCESS: Carriers array appears only once (in first page)';
    ELSE
        RAISE NOTICE 'ISSUE: Carriers array appears % times (should be 1)', carriers_count;
    END IF;
END $$;
