-- Test script for the new multi-class plan design function v2
-- This script tests both single-class and multi-class scenarios

-- Step 1: Check what employee classes exist for your plan
-- Replace 'your-plan-uuid-here' with your actual plan UUID
SELECT 
    p.plan_uuid,
    COUNT(DISTINCT ec.name) as total_employee_classes,
    array_agg(DISTINCT ec.name ORDER BY ec.name) as employee_class_names
FROM sandf.plan p
JOIN sandf.quote q ON q.plan_id = p.plan_id
JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
WHERE p.plan_uuid = 'your-plan-uuid-here'::uuid
AND ecq.formatted_quote_details IS NOT NULL
AND ecq.formatted_quote_details != '{}'::jsonb
GROUP BY p.plan_uuid;

-- Step 2: Test the new v2 function
-- This will automatically detect if it's single or multi-class and handle accordingly
SELECT jsonb_pretty(
    fn_get_plan_design_report_v2(
        'your-plan-uuid-here',  -- Replace with your actual plan UUID
        'your-user-id',         -- Replace with your user ID (optional)
        NULL,                   -- includes_param (optional)
        NULL                    -- excludes_param (optional)
    )
);

-- Step 3: Compare with old function (for single class plans only)
-- This helps verify that single-class behavior is preserved
SELECT 'OLD FUNCTION OUTPUT (single class only):' as comparison;
SELECT jsonb_pretty(
    fn_get_plan_design_report(
        'your-plan-uuid-here',
        'your-user-id',
        NULL,
        NULL
    )
);

SELECT 'NEW V2 FUNCTION OUTPUT (works for both single and multi-class):' as comparison;
SELECT jsonb_pretty(
    fn_get_plan_design_report_v2(
        'your-plan-uuid-here',
        'your-user-id',
        NULL,
        NULL
    )
);

-- Step 4: Test with specific includes/excludes (optional)
SELECT 'TESTING WITH FILTERS:' as test_type;
SELECT jsonb_pretty(
    fn_get_plan_design_report_v2(
        'your-plan-uuid-here',
        'your-user-id',
        ARRAY['lifeInsuranceADAD', 'dependentLife'],  -- Only include these sections
        ARRAY['extendedHealth']                       -- Exclude these sections
    )
);

-- Step 5: Verify the output structure for multi-class
-- For multi-class plans, you should see:
-- - Section IDs like: "lifeinsuranceadad-1classname", "dependentlife-2classname"
-- - Section names like: "Life Insurance & AD&D - 1classname", "Dependent Life - 2classname"
-- - Section properties like: "lifeInsuranceADAD1classname", "dependentLife2classname"

-- Expected output structure for single class:
-- [
--   {
--     "carriers": ["Carrier1", "Carrier2", ...],
--     "sections": [
--       {
--         "id": "lifeinsuranceadad",
--         "name": "Life Insurance & AD&D",
--         "benefits": [...]
--       }
--     ]
--   }
-- ]

-- Expected output structure for multi-class:
-- [
--   {
--     "carriers": ["Carrier1", "Carrier2", ...],
--     "sections": [
--       {
--         "id": "lifeinsuranceadad-1classname",
--         "name": "Life Insurance & AD&D - 1classname",
--         "benefits": [
--           {
--             "key": "coverageLife",
--             "name": "Amount of Coverage",
--             "section": "lifeInsuranceADAD1classname",
--             "values": {...}
--           }
--         ]
--       },
--       {
--         "id": "lifeinsuranceadad-2classname",
--         "name": "Life Insurance & AD&D - 2classname",
--         "benefits": [...]
--       }
--     ]
--   }
-- ]
