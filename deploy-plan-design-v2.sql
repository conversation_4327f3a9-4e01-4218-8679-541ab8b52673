-- Deployment script for the new multi-class plan design function v2
-- Execute this script to create the new function in your database

-- Step 1: Create the function
-- This will read from the plan-design-v2.sql file
\i multi-class/multi-class-support/plan-design-v2.sql

-- Step 2: Verify the function was created successfully
SELECT 
    routine_name,
    routine_type,
    data_type
FROM information_schema.routines 
WHERE routine_name = 'fn_get_plan_design_report_v2'
AND routine_schema = 'sandf';

-- Step 3: Test with a sample plan (replace with your actual plan UUID)
-- Uncomment and modify the following lines to test:

/*
-- Check employee classes for your plan
SELECT 
    p.plan_uuid,
    COUNT(DISTINCT ec.name) as total_employee_classes,
    array_agg(DISTINCT ec.name ORDER BY ec.name) as employee_class_names
FROM sandf.plan p
JOIN sandf.quote q ON q.plan_id = p.plan_id
JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
WHERE p.plan_uuid = 'your-plan-uuid-here'::uuid
AND ecq.formatted_quote_details IS NOT NULL
AND ecq.formatted_quote_details != '{}'::jsonb
GROUP BY p.plan_uuid;

-- Test the new function
SELECT jsonb_pretty(
    sandf.fn_get_plan_design_report_v2(
        'your-plan-uuid-here',
        'your-user-id',
        NULL,
        NULL
    )
);
*/

-- Step 4: Performance comparison (optional)
-- Uncomment to compare performance between old and new functions:

/*
-- Time the old function (single class only)
\timing on
SELECT fn_get_plan_design_report('your-plan-uuid-here', 'your-user-id', NULL, NULL);
\timing off

-- Time the new function
\timing on
SELECT fn_get_plan_design_report_v2('your-plan-uuid-here', 'your-user-id', NULL, NULL);
\timing off
*/

-- Notes:
-- 1. The new function automatically detects single vs multi-class scenarios
-- 2. For single class: Uses optimized original logic
-- 3. For multi-class: Appends class names to section IDs and names
-- 4. Output structure is consistent with your requirements
-- 5. All existing filtering and ordering capabilities are preserved
