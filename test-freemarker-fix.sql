-- Test script to verify the FreeMarker template fix
-- This script tests that the plan-design-multi-class function now generates
-- JSON that is compatible with FreeMarker templates

-- Step 1: Find a plan with multiple employee classes to test with
-- Replace 'your-plan-uuid-here' with an actual plan UUID
SELECT 
    'Step 1: Checking available plans with multiple employee classes' as test_step;

SELECT 
    p.plan_uuid,
    COUNT(DISTINCT ec.name) as total_employee_classes,
    array_agg(DISTINCT ec.name ORDER BY ec.name) as employee_class_names
FROM sandf.plan p
JOIN sandf.quote q ON q.plan_id = p.plan_id
JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
WHERE ecq.formatted_quote_details IS NOT NULL
AND ecq.formatted_quote_details != '{}'::jsonb
GROUP BY p.plan_uuid
HAVING COUNT(DISTINCT ec.name) > 1
ORDER BY total_employee_classes DESC
LIMIT 5;

-- Step 2: Test the fixed function with a multi-class plan
-- Replace 'your-plan-uuid-here' with an actual plan UUID from Step 1
SELECT 
    'Step 2: Testing fixed multi-class function' as test_step;

/*
-- Uncomment and replace with actual plan UUID to test
SELECT jsonb_pretty(
    sandf.fn_get_plan_design_report_multi_class(
        'your-plan-uuid-here',  -- Replace with actual plan UUID
        'your-user-id',         -- Replace with actual user ID (optional)
        NULL,                   -- includes_param (optional)
        NULL                    -- excludes_param (optional)
    )
);
*/

-- Step 3: Validation script to check the JSON structure
-- This script validates that the output has the correct structure for FreeMarker
DO $$
DECLARE
    test_result TEXT;
    parsed_result JSONB;
    page_count INTEGER;
    page_index INTEGER;
    page_obj JSONB;
    section_obj JSONB;
    benefit_obj JSONB;
    carriers_array JSONB;
    carrier_name TEXT;
    benefit_values JSONB;
    validation_errors TEXT[] := ARRAY[]::TEXT[];
    has_carriers BOOLEAN;
    section_index INTEGER;
    benefit_index INTEGER;
BEGIN
    RAISE NOTICE 'Step 3: Validating JSON structure for FreeMarker compatibility';
    
    -- This would be your actual function call
    -- Uncomment and replace with actual parameters to test
    /*
    test_result := sandf.fn_get_plan_design_report_multi_class(
        'your-plan-uuid-here',
        'your-user-id',
        NULL,
        NULL
    );
    */
    
    -- For demonstration, use a sample structure similar to t1.json
    test_result := '[
        {
            "carriers": ["Carrier1", "Carrier2", "Carrier3"],
            "sections": [
                {
                    "id": "section1",
                    "name": "Section 1",
                    "benefits": [
                        {
                            "key": "benefit1",
                            "name": "Benefit 1",
                            "values": {
                                "Carrier1": "Value1",
                                "Carrier2": "Value2",
                                "Carrier3": "Value3"
                            }
                        }
                    ]
                }
            ]
        }
    ]';
    
    -- Parse the JSON result
    BEGIN
        parsed_result := test_result::JSONB;
        RAISE NOTICE '✓ JSON parsing successful';
    EXCEPTION WHEN OTHERS THEN
        validation_errors := validation_errors || 'JSON parsing failed: ' || SQLERRM;
        RAISE NOTICE '✗ JSON parsing failed: %', SQLERRM;
        RETURN;
    END;
    
    -- Check if result is an array
    IF jsonb_typeof(parsed_result) != 'array' THEN
        validation_errors := validation_errors || 'Result is not an array';
        RAISE NOTICE '✗ Result is not an array';
    ELSE
        RAISE NOTICE '✓ Result is an array';
    END IF;
    
    page_count := jsonb_array_length(parsed_result);
    RAISE NOTICE 'Found % pages', page_count;
    
    -- Validate each page
    FOR page_index IN 0..page_count-1 LOOP
        page_obj := parsed_result -> page_index;
        
        -- Check if page has carriers array
        has_carriers := page_obj ? 'carriers';
        IF NOT has_carriers THEN
            validation_errors := validation_errors || format('Page %s missing carriers array', page_index + 1);
            RAISE NOTICE '✗ Page % missing carriers array', page_index + 1;
        ELSE
            RAISE NOTICE '✓ Page % has carriers array', page_index + 1;
            carriers_array := page_obj -> 'carriers';
            
            -- Check if page has sections array
            IF NOT (page_obj ? 'sections') THEN
                validation_errors := validation_errors || format('Page %s missing sections array', page_index + 1);
                RAISE NOTICE '✗ Page % missing sections array', page_index + 1;
            ELSE
                RAISE NOTICE '✓ Page % has sections array', page_index + 1;
                
                -- Validate each section
                FOR section_index IN 0..jsonb_array_length(page_obj -> 'sections')-1 LOOP
                    section_obj := page_obj -> 'sections' -> section_index;
                    
                    -- Validate each benefit in the section
                    IF section_obj ? 'benefits' THEN
                        FOR benefit_index IN 0..jsonb_array_length(section_obj -> 'benefits')-1 LOOP
                            benefit_obj := section_obj -> 'benefits' -> benefit_index;
                            benefit_values := benefit_obj -> 'values';
                            
                            -- Check if benefit has values for all carriers
                            FOR carrier_name IN SELECT jsonb_array_elements_text(carriers_array) LOOP
                                IF NOT (benefit_values ? carrier_name) THEN
                                    validation_errors := validation_errors || format(
                                        'Page %s, Section %s, Benefit %s missing value for carrier %s',
                                        page_index + 1, section_index + 1, benefit_index + 1, carrier_name
                                    );
                                    RAISE NOTICE '✗ Missing value for carrier % in benefit %', carrier_name, benefit_obj ->> 'name';
                                END IF;
                            END LOOP;
                        END LOOP;
                    END IF;
                END LOOP;
            END IF;
        END IF;
    END LOOP;
    
    -- Summary
    IF array_length(validation_errors, 1) IS NULL THEN
        RAISE NOTICE '';
        RAISE NOTICE '🎉 VALIDATION PASSED: JSON structure is compatible with FreeMarker templates';
        RAISE NOTICE 'All pages have carriers arrays and all benefits have values for all carriers';
    ELSE
        RAISE NOTICE '';
        RAISE NOTICE '❌ VALIDATION FAILED: Found % issues:', array_length(validation_errors, 1);
        FOR i IN 1..array_length(validation_errors, 1) LOOP
            RAISE NOTICE '   - %', validation_errors[i];
        END LOOP;
    END IF;
    
END;
$$;

-- Step 4: Instructions for actual testing
SELECT 
    'Step 4: Instructions for actual testing' as test_step;

SELECT 'To test with your actual data:' as instruction
UNION ALL
SELECT '1. Run Step 1 to find a plan UUID with multiple employee classes'
UNION ALL  
SELECT '2. Replace "your-plan-uuid-here" in Step 2 with the actual UUID'
UNION ALL
SELECT '3. Replace "your-user-id" with your actual user ID'
UNION ALL
SELECT '4. Uncomment and run the function call in Step 2'
UNION ALL
SELECT '5. Replace the test_result assignment in Step 3 with the actual function call'
UNION ALL
SELECT '6. Run the validation to ensure FreeMarker compatibility';
