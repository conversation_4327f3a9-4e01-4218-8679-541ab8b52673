# Multi-Class Plan Design Function

## Overview

The new `fn_get_plan_design_report_v2` function extends the original plan design functionality to support multiple employee classes dynamically, instead of being hardcoded to only work with 'RTQ' employee class.

## Key Improvements

### 1. Dynamic Employee Class Detection
- **Old**: Hardcoded to only process `ec.name = 'RTQ'`
- **New**: Automatically detects all employee classes for a plan and processes them dynamically

### 2. Adaptive Processing Logic
- **Single Class**: If only one employee class exists, uses the original optimized logic
- **Multiple Classes**: If multiple employee classes exist, iterates through each one separately

### 3. Enhanced Output Structure
- **Single Class**: Returns array with one object containing employee_class, carriers, and sections
- **Multiple Classes**: Returns array with one consolidated object containing all carriers and sections from all employee classes, with class names appended to section names

## Function Signature

```sql
CREATE OR REPLACE FUNCTION fn_get_plan_design_report_v2(
    plan_uuid_param TEXT,
    user_id_param TEXT DEFAULT NULL,
    includes_param TEXT[] DEFAULT NULL,
    excludes_param TEXT[] DEFAULT NULL
)
RETURNS JSONB
```

## Output Structure

### Single Employee Class
```json
[
  {
    "employee_class": "RTQ",
    "carriers": ["Carrier A", "Carrier B"],
    "sections": [
      {
        "name": "Section Name",
        "id": "sectionid",
        "benefits": [...]
      }
    ]
  }
]
```

### Multiple Employee Classes (With Class Names Appended to Sections)
```json
[
  {
    "carriers": ["Carrier A", "Carrier B"],
    "sections": [
      {
        "id": "lifeinsuranceadad-rtq",
        "name": "Life Insurance & AD&D - RTQ",
        "benefits": [...]
      },
      {
        "id": "lifeinsuranceadad-management",
        "name": "Life Insurance & AD&D - Management",
        "benefits": [...]
      },
      {
        "id": "lifeinsuranceadad-union",
        "name": "Life Insurance & AD&D - Union",
        "benefits": [...]
      },
      {
        "id": "dependentlife-rtq",
        "name": "Dependent Life - RTQ",
        "benefits": [...]
      },
      {
        "id": "dependentlife-management",
        "name": "Dependent Life - Management",
        "benefits": [...]
      },
      {
        "id": "dependentlife-union",
        "name": "Dependent Life - Union",
        "benefits": [...]
      }
    ]
  }
]
```

## Recent Changes (Multi-Class Section Naming)

The function has been updated to handle multi-class plans by appending employee class names to section IDs and names instead of separating by employee class objects:

### Key Changes:
1. **Section ID Format**: `'lifeinsuranceadad-classname'` instead of separate objects per class
2. **Section Name Format**: `'Life Insurance & AD&D - classname'` with class name appended
3. **Single Output Object**: All classes consolidated into one carriers array and one sections array
4. **Benefit Section References**: Updated to include class names in section references

### Benefits:
- Simpler template rendering logic
- All data in single paginated structure
- Consistent carrier ordering across all classes
- Maintains class distinction through naming convention

## How It Works

### Step 1: Employee Class Detection
```sql
SELECT COUNT(DISTINCT ec.name), array_agg(DISTINCT ec.name ORDER BY ec.name)
INTO employee_class_count, employee_classes
FROM sandf.plan p
JOIN sandf.quote q ON q.plan_id = p.plan_id
JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
WHERE p.plan_uuid = plan_uuid_param::uuid
```

### Step 2: Processing Logic
- **If `employee_class_count = 1`**: Use original single-class logic for optimal performance
- **If `employee_class_count > 1`**: Iterate through each employee class

### Step 3: Data Alignment
For multiple classes, the function ensures:
- Each employee class gets its own section with class name appended to section ID and name
- Section IDs follow format: `'lifeinsuranceadad-1classname'`, `'dependentlife-2classname'`
- Section names follow format: `'Life Insurance & AD&D - 1classname'`, `'Dependent Life - 2classname'`
- Carriers are ordered consistently across all employee classes
- Benefits are aligned properly within each class
- Same skip logic and validation rules apply to all classes

## Usage Examples

### Basic Usage
```sql
SELECT fn_get_plan_design_report_v2('your-plan-uuid');
```

### With User Preferences
```sql
SELECT fn_get_plan_design_report_v2(
    'your-plan-uuid',
    'user-123'
);
```

### With Filters
```sql
SELECT fn_get_plan_design_report_v2(
    'your-plan-uuid',
    'user-123',
    ARRAY['health', 'dental'],  -- Only include these sections
    ARRAY['life']               -- Exclude these sections
);
```

## Migration from Old Function

### Old Function Call
```sql
SELECT fn_get_plan_design_report('plan-uuid', 'user-id');
```

### New Function Call
```sql
SELECT fn_get_plan_design_report_v2('plan-uuid', 'user-id');
```

### Key Differences in Output
1. **Structure**: New function always returns an array, even for single class
2. **Employee Class Info**: Each result object includes `employee_class` field
3. **Multiple Classes**: New function can return multiple objects for different employee classes

## Testing

Use the provided `check-employee-classes.sql` to understand your data structure:

```sql
-- Check how many employee classes exist
SELECT 
    COUNT(DISTINCT ec.name) as total_classes,
    array_agg(DISTINCT ec.name) as class_names
FROM sandf.plan p
JOIN sandf.quote q ON q.plan_id = p.plan_id
JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
WHERE p.plan_uuid = 'your-plan-uuid'::uuid;
```

## Benefits

1. **Backward Compatibility**: Works with existing single-class plans
2. **Future-Proof**: Automatically handles new employee classes
3. **Performance**: Optimized path for single-class scenarios
4. **Consistency**: Same business logic and validation rules across all classes
5. **Flexibility**: Maintains all existing filtering and ordering capabilities
