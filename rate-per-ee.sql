
            DECLARE
    benefit_premiums_key TEXT := 'benefitPremiums';
    rtq_name TEXT := 'RTQ';
    quote_record RECORD;
    benefit_premiums JSONB;
    carriers_array JSONB := '[]'::jsonb;
    sections_array JSONB := '[]'::jsonb;
    carrier_name TEXT;
    rate_types TEXT[] := ARRAY['single', 'couple', 'family'];
    rate_display TEXT[] := ARRAY['Single', 'Couple', 'Family'];
	additional_param TEXT[];
    type_rate_map JSONB := '{}'::jsonb;
    shared_amount NUMERIC := 0;
    total_premium NUMERIC;
    formatted_premium TEXT;
    benefit_obj JSONB;
    section_obj JSONB;
    benefits_array JSONB := '[]'::jsonb;
    i INT;
    rate_type TEXT;
    rate_name TEXT;
    benefit_map JSONB := '{}'::jsonb;

    carrier_order_map JSONB := '{}'::jsonb;
    ordered_carriers_array JSONB := '[]'::jsonb;
    carrier_order INTEGER;
    premium_key TEXT;
    premium_obj JSONB;
    rate_obj JSONB;
    premium_value NUMERIC;
    premium_text TEXT;

    -- Config variables
    config_json JSONB;
    benefit_to_premium_map JSONB;
    included_premium_per_ee TEXT[];
    excluded_premium_shared_cost_rates_per_employee TEXT[];
    mapped_premium_key TEXT;
    should_include_key BOOLEAN;
    is_included_premium_per_ee BOOLEAN;
    is_excluded_from_shared_cost BOOLEAN;
BEGIN
    -- Get configuration from config table
    SELECT json_data INTO config_json
    FROM config.json_storage
    WHERE properties = '{"config": "SURVEY_REPORT_CONFIG"}'
    LIMIT 1;

    -- Extract configuration mappings
    IF config_json IS NOT NULL THEN
        benefit_to_premium_map := config_json -> 'benefitToPremium';

        -- Extract included_premium_per_ee array
        IF config_json ? 'included_premium_per_ee' THEN
            SELECT array_agg(value::text)
            INTO included_premium_per_ee
            FROM jsonb_array_elements_text(config_json -> 'included_premium_per_ee');
        ELSE
            included_premium_per_ee := ARRAY[]::text[];
        END IF;

        -- Extract excluded_premium_shared_cost_rates_per_employee array
        IF config_json ? 'excluded_premium_shared_cost_rates_per_employee' THEN
            SELECT array_agg(value::text)
            INTO excluded_premium_shared_cost_rates_per_employee
            FROM jsonb_array_elements_text(config_json -> 'excluded_premium_shared_cost_rates_per_employee');
        ELSE
            excluded_premium_shared_cost_rates_per_employee := ARRAY[]::text[];
        END IF;

        -- Keep backward compatibility for employeeAssistancePremium
        additional_param := config_json -> 'employeeAssistancePremium';
    ELSE
        benefit_to_premium_map := '{}'::jsonb;
        included_premium_per_ee := ARRAY[]::text[];
        excluded_premium_shared_cost_rates_per_employee := ARRAY[]::text[];
        additional_param := ARRAY[]::text[];
    END IF;

    -- Debug logging for configuration
    RAISE NOTICE 'Configuration loaded:';
    RAISE NOTICE 'benefit_to_premium_map: %', benefit_to_premium_map;
    RAISE NOTICE 'included_premium_per_ee: %', included_premium_per_ee;
    RAISE NOTICE 'excluded_premium_shared_cost_rates_per_employee: %', excluded_premium_shared_cost_rates_per_employee;
    RAISE NOTICE 'additional_param (legacy): %', additional_param;

    RAISE NOTICE 'Step 1: Build carrier order map...';

    FOR quote_record IN
        SELECT ecq.formatted_quote_details,
               c.description AS carrier_description,
               q.quote_id,
               q.quote_uuid
        FROM sandf.plan p
        JOIN sandf.quote q ON q.plan_id = p.plan_id
        JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
        JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
        JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
        WHERE p.plan_uuid = plan_uuid_param::uuid
          AND ec.name = rtq_name
          AND ecq.formatted_quote_details IS NOT NULL
          AND ecq.formatted_quote_details != '{}'::jsonb
    LOOP
        carrier_name := quote_record.carrier_description;

        carrier_order := sandf.get_user_preference_order(
            user_id_param,
            plan_uuid_param::text,
            quote_record.quote_id::bigint,
            quote_record.quote_uuid,
            999999
        );

        RAISE NOTICE 'Carrier: %, Order: %', carrier_name, carrier_order;

        IF NOT carrier_order_map ? carrier_name THEN
            carrier_order_map := carrier_order_map || jsonb_build_object(
                carrier_name,
                jsonb_build_object(
                    'order', carrier_order,
                    'quote_id', quote_record.quote_id,
                    'quote_uuid', quote_record.quote_uuid
                )
            );
        END IF;
    END LOOP;

    RAISE NOTICE 'Step 2: Process quotes in order...';

    FOR quote_record IN
            SELECT ecq.formatted_quote_details,
                c.description AS carrier_description,
                q.quote_id,
                q.quote_uuid
            FROM sandf.plan p
            JOIN sandf.quote q ON q.plan_id = p.plan_id
            JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
            JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
            JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
            WHERE p.plan_uuid = plan_uuid_param::uuid
            AND ec.name = rtq_name
            AND ecq.formatted_quote_details IS NOT NULL
            AND ecq.formatted_quote_details != '{}'::jsonb
            ORDER BY sandf.get_user_preference_order(
                user_id_param,
                plan_uuid_param::text,
                q.quote_id::bigint,
                q.quote_uuid,
                999999
            ) ASC, c.description ASC
    LOOP
        carrier_name := quote_record.carrier_description;
        RAISE NOTICE 'Processing carrier: %', carrier_name;

        IF NOT carriers_array ? carrier_name THEN
            carriers_array := carriers_array || jsonb_build_array(carrier_name);
        END IF;


        benefit_premiums := quote_record.formatted_quote_details -> benefit_premiums_key;
        RAISE NOTICE 'benefit_premiums --: %', benefit_premiums;

        IF benefit_premiums IS NOT NULL AND jsonb_typeof(benefit_premiums) = 'object' THEN

            type_rate_map := '{"single": 0, "couple": 0, "family": 0, "shared": 0}'::jsonb;

            -- Simple premium processing logic using config mapping
            -- Step 1: Process Extended Health and Dental tiered premiums (single, couple, family)
            FOR premium_key IN SELECT jsonb_object_keys(benefit_premiums)
            LOOP
                premium_obj := benefit_premiums -> premium_key;

                -- Get mapped key name from config
                IF benefit_to_premium_map ? premium_key THEN
                    mapped_premium_key := benefit_to_premium_map ->> premium_key;
                ELSE
                    mapped_premium_key := premium_key;
                END IF;

                RAISE NOTICE 'Processing premium_key: %, mapped_key: %', premium_key, mapped_premium_key;
                RAISE NOTICE 'premium_obj: %', premium_obj;

                -- Only process Extended Health and Dental for tiered rates (hardcoded for now)
                IF mapped_premium_key = 'extendedHealth' OR mapped_premium_key = 'dental' THEN
                    -- Handle tiered premiums (single, couple, family)
                    IF premium_obj ? 'single' OR premium_obj ? 'couple' OR premium_obj ? 'family' THEN
                        FOR rate_type IN SELECT unnest(rate_types)
                        LOOP
                            rate_obj := premium_obj -> rate_type;
                            IF rate_obj IS NOT NULL AND jsonb_typeof(rate_obj) = 'object' THEN
                                premium_text := rate_obj ->> 'rate';
                                premium_value := sandf.safe_parse_numeric(premium_text);
                                type_rate_map := jsonb_set(
                                    type_rate_map,
                                    ARRAY[rate_type],
                                    to_jsonb(COALESCE((type_rate_map ->> rate_type)::NUMERIC, 0) + premium_value)
                                );
                                RAISE NOTICE 'Added % premium (%): %', rate_type, premium_key, premium_value;
                            END IF;
                        END LOOP;
                    END IF;
                END IF;
            END LOOP;

            -- Step 2: Process ALL other premiums for shared cost (skip extendedHealth, dental, employeeAssistance, dependentLife)
            FOR premium_key IN SELECT jsonb_object_keys(benefit_premiums)
            LOOP
                premium_obj := benefit_premiums -> premium_key;

                -- Get mapped key name from config
                IF benefit_to_premium_map ? premium_key THEN
                    mapped_premium_key := benefit_to_premium_map ->> premium_key;
                ELSE
                    mapped_premium_key := premium_key;
                END IF;

                -- Check if this premium should be included in shared cost calculation
                is_included_premium_per_ee := mapped_premium_key = ANY(included_premium_per_ee);
                is_excluded_from_shared_cost := mapped_premium_key = ANY(excluded_premium_shared_cost_rates_per_employee);

                -- Skip: extendedHealth, dental (tiered), included_premium_per_ee items, excluded items
                IF NOT (mapped_premium_key = 'extendedHealth' OR
                        mapped_premium_key = 'dental' OR
                        is_included_premium_per_ee OR
                        is_excluded_from_shared_cost) THEN
                    -- Add the premium value to shared cost

                    premium_text := premium_obj ->> 'premium';
                    RAISE NOTICE 'Processing premium_key: %, mapped_key: %', premium_obj, premium_key;
                    RAISE NOTICE 'premium_text-----------------: %', premium_text;
                    premium_value := sandf.safe_parse_numeric(premium_text);
                    RAISE NOTICE 'premium_value-----------------: %', premium_value;
                    type_rate_map := jsonb_set(
                        type_rate_map,
                        ARRAY['shared'],
                        to_jsonb(COALESCE((type_rate_map ->> 'shared')::NUMERIC, 0) + premium_value)
                    );
                    RAISE NOTICE 'Added to shared cost (%) mapped as (%): %', premium_key, mapped_premium_key, premium_value;
                ELSE
                    RAISE NOTICE 'Skipped from shared cost (%) mapped as (%) - Reason: tiered=%, included_per_ee=%, excluded=%',
                        premium_key, mapped_premium_key,
                        (mapped_premium_key = 'extendedHealth' OR mapped_premium_key = 'dental'),
                        is_included_premium_per_ee,
                        is_excluded_from_shared_cost;
                END IF;
            END LOOP;

            RAISE NOTICE 'Final type_rate_map before shared calculation: %', type_rate_map;
            RAISE NOTICE 'number_of_employees_param: %', number_of_employees_param;

            -- Calculate shared amount (all non-tiered, non-employee-assistance premiums divided by employees)
            shared_amount := COALESCE((type_rate_map ->> 'shared')::NUMERIC, 0) / number_of_employees_param;
            RAISE NOTICE 'Calculated shared_amount: %', shared_amount;

            -- Build final premium amounts for each rate type
            FOR i IN 1..array_length(rate_types, 1)
            LOOP
                rate_type := rate_types[i];
                rate_name := rate_display[i];
                total_premium := COALESCE((type_rate_map ->> rate_type)::NUMERIC, 0) + shared_amount;
                formatted_premium := sandf.fn_format_currency_with_symbol_java_style(total_premium);

                IF NOT benefit_map ? rate_type THEN
                    benefit_map := benefit_map || jsonb_build_object(
                        rate_type,
                        jsonb_build_object(
                            'name', rate_name,
                            'key', rate_type,
                            'values', '{}'::jsonb
                        )
                    );
                END IF;

                benefit_map := jsonb_set(
                    benefit_map,
                    ARRAY[rate_type, 'values', carrier_name],
                    to_jsonb(formatted_premium)
                );
                RAISE NOTICE 'Final % for %: %', rate_type, carrier_name, formatted_premium;
            END LOOP;
        END IF;
    END LOOP;

    -- Build final benefits section
    FOR rate_type IN SELECT unnest(rate_types)
    LOOP
        IF benefit_map ? rate_type THEN
            benefit_obj := benefit_map -> rate_type;
            benefits_array := benefits_array || jsonb_build_array(benefit_obj);
        END IF;
    END LOOP;

    section_obj := jsonb_build_object(
        'name', 'Rates Per Employee',
        'id', 'ratesPerEmployee',
        'benefits', benefits_array
    );
    sections_array := sections_array || jsonb_build_array(section_obj);

    RAISE NOTICE 'Returning final JSON structure.';

    RETURN jsonb_build_object(
        'carriers', carriers_array,
        'sections', sections_array
    );
END;
            