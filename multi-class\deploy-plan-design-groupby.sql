-- Deployment script for the new plan design multi-class function with groupby logic
-- This script deploys the updated function that groups sections by matching benefits

-- First, backup the original function (optional)
-- CREATE OR REPLACE FUNCTION sandf.fn_get_plan_design_report_multi_class_original AS 
-- (copy of original function for backup)

-- Deploy the new function with groupby logic
\i multi-class/mutli-class.sql

-- Verify the function was created successfully
SELECT 
    routine_name,
    routine_type,
    data_type
FROM information_schema.routines 
WHERE routine_schema = 'sandf' 
AND routine_name = 'fn_get_plan_design_report_multi_class';

-- Test with a sample plan (replace with actual plan UUID)
/*
SELECT jsonb_pretty(
    sandf.fn_get_plan_design_report_multi_class(
        'your-plan-uuid-here',
        'your-user-id'
    )
);
*/

/*
Key changes in the new function:

1. Added class-to-letter mapping (A, B, C...) for groupby logic
2. Collects section data by class first, then groups by matching benefit values
3. Creates combined sections with appropriate suffixes:
   - "ALL" when benefits match across all classes
   - "A", "B", "C" when benefits differ between classes
4. Maintains proper ordering: ALL first, then A, B, C, then combinations
5. Follows the same pattern as rate-sheet-v2.sql for consistency

Output format changes:
- Old: "Life Insurance & AD&D - Class 1 - Salaried EE's"
- New: "Life Insurance & AD&D A" (if benefits differ) or "Life Insurance & AD&D ALL" (if benefits match)

This provides the plan-design-new.json format you requested.
*/
