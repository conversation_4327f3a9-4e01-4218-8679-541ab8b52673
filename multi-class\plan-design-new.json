[
  {
    "carriers": [
      "Equitable",
      "Canada Life",
      "Benefits by Design",
      "Manulife",
      "Sunlife"
    ],
    "sections": [
      {
        "id": "lifeinsuranceadadA",
        "name": "Life Insurance & AD&D A",
        "benefits": [
          {
            "key": "coverageLife",
            "name": "Amount of Coverage",
            "values": {
              "Sunlife": "$25,000",
              "Manulife": "$25,000",
              "Equitable": "2 times annual earnings",
              "Canada Life": "2 times annual earnings",
              "Benefits by Design": "$25,000"
            },
            "section": "lifeinsuranceadadA"
          },
          {
            "key": "ageReduction",
            "name": "Age Reduction",
            "values": {
              "Sunlife": "50% at age 65",
              "Manulife": "50% at age 65",
              "Equitable": "70",
              "Canada Life": "50% at age 65",
              "Benefits by Design": "50% at age 65, $25,000 age 70"
            },
            "section": "lifeinsuranceadadA"
          },
          {
            "key": "terminationAgeLife",
            "name": "Termination Age - Life",
            "values": {
              "Sunlife": "Retirement or to age 70",
              "Manulife": "-",
              "Equitable": "-",
              "Canada Life": "-",
              "Benefits by Design": "Retirement or to age 75"
            },
            "section": "lifeinsuranceadadA"
          },
          {
            "key": "terminationAgeADAD",
            "name": "Termination Age - AD&D",
            "values": {
              "Sunlife": "Retirement or to age 70",
              "Manulife": "-",
              "Equitable": "-",
              "Canada Life": "-",
              "Benefits by Design": "Retirement or to age 75"
            },
            "section": "lifeinsuranceadadA"
          }
        ]
      },
      {
        "id": "lifeinsuranceadadB",
        "name": "Life Insurance & AD&D B",
        "benefits": [
          {
            "key": "coverageLife",
            "name": "Amount of Coverage",
            "values": {
              "Sunlife": "$25,000",
              "Manulife": "$25,000",
              "Equitable": "$25,000",
              "Canada Life": "2 times annual earnings",
              "Benefits by Design": "$25,000"
            },
            "section": "lifeinsuranceadadB"
          },
          {
            "key": "ageReduction",
            "name": "Age Reduction",
            "values": {
              "Sunlife": "50% at age 65",
              "Manulife": "50% at age 65",
              "Equitable": "50% at age 65",
              "Canada Life": "65% at age 65, 50% at age 70",
              "Benefits by Design": "50% at age 65, $25,000 age 70"
            },
            "section": "lifeinsuranceadadB"
          },
          {
            "key": "terminationAgeLife",
            "name": "Termination Age - Life",
            "values": {
              "Sunlife": "Retirement or to age 70",
              "Manulife": "-",
              "Equitable": "-",
              "Canada Life": "-",
              "Benefits by Design": "Retirement or to age 75"
            },
            "section": "lifeinsuranceadadB"
          },
          {
            "key": "terminationAgeADAD",
            "name": "Termination Age - AD&D",
            "values": {
              "Sunlife": "Retirement or to age 70",
              "Manulife": "-",
              "Equitable": "-",
              "Canada Life": "-",
              "Benefits by Design": "Retirement or to age 75"
            },
            "section": "lifeinsuranceadadB"
          }
        ]
      },
      {
        "id": "dependentlifeALL",
        "name": "Dependent Life ALL",
        "benefits": [
          {
            "key": "spouse",
            "name": "Spouse",
            "values": {
              "Sunlife": "-",
              "Manulife": "-",
              "Equitable": "-",
              "Canada Life": "-",
              "Benefits by Design": "-"
            },
            "section": "dependentlifeALL"
          },
          {
            "key": "child",
            "name": "Child",
            "values": {
              "Sunlife": "-",
              "Manulife": "-",
              "Equitable": "-",
              "Canada Life": "-",
              "Benefits by Design": "-"
            },
            "section": "dependentlifeALL"
          }
        ]
      }
    ]
  },
  {
    "carriers": [
      "Equitable",
      "Canada Life",
      "Benefits by Design",
      "Manulife",
      "Sunlife"
    ],
    "sections": [
      {
        "id": "extendedhealthA",
        "name": "Extended Health A",
        "benefits": [
          {
            "key": "annualDeductibleEHC",
            "name": "Annual Deductible",
            "values": {
              "Sunlife": "Nil",
              "Manulife": "Nil",
              "Equitable": "$25",
              "Canada Life": "$50",
              "Benefits by Design": "Nil"
            },
            "section": "extendedhealthA"
          },
          {
            "key": "coInsuranceEHC",
            "name": "CoInsurance",
            "values": {
              "Sunlife": "80%",
              "Manulife": "100%",
              "Equitable": "100%",
              "Canada Life": "80%",
              "Benefits by Design": "100%"
            },
            "section": "extendedhealthA"
          },
          {
            "key": "paramedicalMaximum",
            "name": "Paramedical Maximum",
            "values": {
              "Sunlife": "$500",
              "Manulife": "unlimited",
              "Equitable": "$500",
              "Canada Life": "$500",
              "Benefits by Design": "$500"
            },
            "section": "extendedhealthA"
          },
          {
            "key": "psychologistOrSpeechTherapist",
            "name": "Psychologist/Speech Therapist",
            "values": {
              "Sunlife": "$500",
              "Manulife": "-",
              "Equitable": "-",
              "Canada Life": "-",
              "Benefits by Design": "$500"
            },
            "section": "extendedhealthA"
          },
          {
            "key": "visionCare",
            "name": "Vision",
            "values": {
              "Sunlife": "Not Covered",
              "Manulife": "Not Covered",
              "Equitable": "$500/24 months",
              "Canada Life": "$300/24 months",
              "Benefits by Design": "Not Covered"
            },
            "section": "extendedhealthA"
          },
          {
            "key": "eyeExams",
            "name": "Eye Exams",
            "values": {
              "Sunlife": "Yes",
              "Manulife": "-",
              "Equitable": "-",
              "Canada Life": "-",
              "Benefits by Design": "Yes"
            },
            "section": "extendedhealthA"
          },
          {
            "key": "privateDutyNursingMaximum",
            "name": "Private Duty Nursing Maximum",
            "values": {
              "Sunlife": "$10,000/year",
              "Manulife": "-",
              "Equitable": "-",
              "Canada Life": "-",
              "Benefits by Design": "$10,000/year"
            },
            "section": "extendedhealthA"
          },
          {
            "key": "semiPrivateHospital",
            "name": "Semi Private Hospital",
            "values": {
              "Sunlife": "Yes",
              "Manulife": "Yes",
              "Equitable": "Yes",
              "Canada Life": "Yes",
              "Benefits by Design": "Yes"
            },
            "section": "extendedhealthA"
          }
        ]
      }
    ]
  },
  {
    "carriers": [
      "Equitable",
      "Canada Life",
      "Benefits by Design",
      "Manulife",
      "Sunlife"
    ],
    "sections": [
      {
        "id": "extendedhealthA",
        "name": "Extended Health A",
        "benefits": [
          {
            "key": "hearingAids",
            "name": "Hearing Aids",
            "values": {
              "Sunlife": "$500/5 years",
              "Manulife": "-",
              "Equitable": "-",
              "Canada Life": "-",
              "Benefits by Design": "$700/5 years"
            },
            "section": "extendedhealthA"
          },
          {
            "key": "outOfCountryTravel",
            "name": "Out of Country Emergency",
            "values": {
              "Sunlife": "Yes",
              "Manulife": "Yes",
              "Equitable": "Yes",
              "Canada Life": "Yes",
              "Benefits by Design": "Yes"
            },
            "section": "extendedhealthA"
          },
          {
            "key": "terminationAgeEHC",
            "name": "Termination Age",
            "values": {
              "Sunlife": "Retirement or to age 85",
              "Manulife": "-",
              "Equitable": "-",
              "Canada Life": "-",
              "Benefits by Design": "Retirement or to age 75"
            },
            "section": "extendedhealthA"
          }
        ]
      },
      {
        "id": "extendedhealthB",
        "name": "Extended Health B",
        "benefits": [
          {
            "key": "annualDeductibleEHC",
            "name": "Annual Deductible",
            "values": {
              "Sunlife": "Nil",
              "Manulife": "-",
              "Equitable": "-",
              "Canada Life": "$25/$50",
              "Benefits by Design": "Nil"
            },
            "section": "extendedhealthB"
          },
          {
            "key": "coInsuranceEHC",
            "name": "CoInsurance",
            "values": {
              "Sunlife": "100%",
              "Manulife": "100%",
              "Equitable": "100%",
              "Canada Life": "80%",
              "Benefits by Design": "100%"
            },
            "section": "extendedhealthB"
          },
          {
            "key": "paramedicalMaximum",
            "name": "Paramedical Maximum",
            "values": {
              "Sunlife": "$500",
              "Manulife": "-",
              "Equitable": "-",
              "Canada Life": "$500",
              "Benefits by Design": "$500"
            },
            "section": "extendedhealthB"
          },
          {
            "key": "psychologistOrSpeechTherapist",
            "name": "Psychologist/Speech Therapist",
            "values": {
              "Sunlife": "$500",
              "Manulife": "-",
              "Equitable": "-",
              "Canada Life": "-",
              "Benefits by Design": "$500"
            },
            "section": "extendedhealthB"
          },
          {
            "key": "visionCare",
            "name": "Vision",
            "values": {
              "Sunlife": "Not Covered",
              "Manulife": "-",
              "Equitable": "-",
              "Canada Life": "$200/24 months",
              "Benefits by Design": "Not Covered"
            },
            "section": "extendedhealthB"
          },
          {
            "key": "eyeExams",
            "name": "Eye Exams",
            "values": {
              "Sunlife": "Yes",
              "Manulife": "-",
              "Equitable": "-",
              "Canada Life": "-",
              "Benefits by Design": "Yes"
            },
            "section": "extendedhealthB"
          },
          {
            "key": "privateDutyNursingMaximum",
            "name": "Private Duty Nursing Maximum",
            "values": {
              "Sunlife": "$10,000/year",
              "Manulife": "-",
              "Equitable": "-",
              "Canada Life": "-",
              "Benefits by Design": "$10,000/year"
            },
            "section": "extendedhealthB"
          }
        ]
      }
    ]
  },
  {
    "carriers": [
      "Equitable",
      "Canada Life",
      "Benefits by Design",
      "Manulife",
      "Sunlife"
    ],
    "sections": [
      {
        "id": "extendedhealthB",
        "name": "Extended Health B",
        "benefits": [
          {
            "key": "semiPrivateHospital",
            "name": "Semi Private Hospital",
            "values": {
              "Sunlife": "Yes",
              "Manulife": "Yes",
              "Equitable": "-",
              "Canada Life": "Yes",
              "Benefits by Design": "Yes"
            },
            "section": "extendedhealthB"
          },
          {
            "key": "hearingAids",
            "name": "Hearing Aids",
            "values": {
              "Sunlife": "$500/5 years",
              "Manulife": "-",
              "Equitable": "-",
              "Canada Life": "-",
              "Benefits by Design": "$700/5 years"
            },
            "section": "extendedhealthB"
          },
          {
            "key": "outOfCountryTravel",
            "name": "Out of Country Emergency",
            "values": {
              "Sunlife": "Yes",
              "Manulife": "Yes",
              "Equitable": "Yes",
              "Canada Life": "Yes",
              "Benefits by Design": "Yes"
            },
            "section": "extendedhealthB"
          },
          {
            "key": "terminationAgeEHC",
            "name": "Termination Age",
            "values": {
              "Sunlife": "Retirement or to age 85",
              "Manulife": "-",
              "Equitable": "-",
              "Canada Life": "-",
              "Benefits by Design": "Retirement or to age 75"
            },
            "section": "extendedhealthB"
          }
        ]
      },
      {
        "id": "prescriptiondrugsA",
        "name": "Prescription Drugs A",
        "benefits": [
          {
            "key": "drugDeductible",
            "name": "Drug Deductible",
            "values": {
              "Sunlife": "Nil",
              "Manulife": "Nil",
              "Equitable": "Nil",
              "Canada Life": "Nil",
              "Benefits by Design": "Nil"
            },
            "section": "prescriptiondrugsA"
          },
          {
            "key": "prescriptionDrugCoInsurance",
            "name": "Prescription Drug Co-Insurance",
            "values": {
              "Sunlife": "80%",
              "Manulife": "100%",
              "Equitable": "100%",
              "Canada Life": "80%",
              "Benefits by Design": "80%"
            },
            "section": "prescriptiondrugsA"
          },
          {
            "key": "prescriptionMaximum",
            "name": "Prescription Maximum",
            "values": {
              "Sunlife": "unlimited",
              "Manulife": "unlimited",
              "Equitable": "unlimited",
              "Canada Life": "unlimited",
              "Benefits by Design": "unlimited"
            },
            "section": "prescriptiondrugsA"
          },
          {
            "key": "prescriptionDrugType",
            "name": "Prescription Drug Type",
            "values": {
              "Sunlife": "Generic",
              "Manulife": "National Formulary",
              "Equitable": "Generic",
              "Canada Life": "Generic Enhanced",
              "Benefits by Design": "Generic"
            },
            "section": "prescriptiondrugsA"
          },
          {
            "key": "prescriptionPayDirectDrugCard",
            "name": "Pay Direct Drug Card",
            "values": {
              "Sunlife": "Yes",
              "Manulife": "-",
              "Equitable": "-",
              "Canada Life": "-",
              "Benefits by Design": "Yes"
            },
            "section": "prescriptiondrugsA"
          },
          {
            "key": "reimbursement",
            "name": "Reimbursement Type",
            "values": {
              "Sunlife": "Pay Direct",
              "Manulife": "Pay Direct",
              "Equitable": "Pay Direct",
              "Canada Life": "Pay Direct",
              "Benefits by Design": "Pay Direct"
            },
            "section": "prescriptiondrugsA"
          }
        ]
      }
    ]
  },
  {
    "carriers": [
      "Equitable",
      "Canada Life",
      "Benefits by Design",
      "Manulife",
      "Sunlife"
    ],
    "sections": [
      {
        "id": "prescriptiondrugsB",
        "name": "Prescription Drugs B",
        "benefits": [
          {
            "key": "drugDeductible",
            "name": "Drug Deductible",
            "values": {
              "Sunlife": "Nil",
              "Manulife": "-",
              "Equitable": "-",
              "Canada Life": "Nil",
              "Benefits by Design": "Nil"
            },
            "section": "prescriptiondrugsB"
          },
          {
            "key": "prescriptionDrugCoInsurance",
            "name": "Prescription Drug Co-Insurance",
            "values": {
              "Sunlife": "80%",
              "Manulife": "-",
              "Equitable": "-",
              "Canada Life": "80%",
              "Benefits by Design": "80%"
            },
            "section": "prescriptiondrugsB"
          },
          {
            "key": "prescriptionMaximum",
            "name": "Prescription Maximum",
            "values": {
              "Sunlife": "unlimited",
              "Manulife": "-",
              "Equitable": "-",
              "Canada Life": "unlimited",
              "Benefits by Design": "unlimited"
            },
            "section": "prescriptiondrugsB"
          },
          {
            "key": "prescriptionDrugType",
            "name": "Prescription Drug Type",
            "values": {
              "Sunlife": "Generic",
              "Manulife": "-",
              "Equitable": "Generic",
              "Canada Life": "Generic Enhanced",
              "Benefits by Design": "Generic"
            },
            "section": "prescriptiondrugsB"
          },
          {
            "key": "prescriptionPayDirectDrugCard",
            "name": "Pay Direct Drug Card",
            "values": {
              "Sunlife": "Yes",
              "Manulife": "-",
              "Equitable": "-",
              "Canada Life": "-",
              "Benefits by Design": "Yes"
            },
            "section": "prescriptiondrugsB"
          },
          {
            "key": "reimbursement",
            "name": "Reimbursement Type",
            "values": {
              "Sunlife": "Pay Direct",
              "Manulife": "-",
              "Equitable": "-",
              "Canada Life": "Pay Direct",
              "Benefits by Design": "Pay Direct"
            },
            "section": "prescriptiondrugsB"
          }
        ]
      }
    ]
  }
  ......
]