-- Test script to verify the multi-class section ID fix
-- This script tests the section ID generation logic

-- Test the section ID generation logic
DO $$
DECLARE
    section_name TEXT := 'lifeInsuranceADADRTQ';
    original_section_name TEXT := 'lifeInsuranceADAD';
    class_suffix_extracted TEXT;
    section_id TEXT;
BEGIN
    -- Extract class suffix from section name
    class_suffix_extracted := replace(section_name, original_section_name, '');
    section_id := lower(replace(original_section_name, ' ', '')) || '-' || lower(class_suffix_extracted);
    
    RAISE NOTICE 'Section Name: %', section_name;
    RAISE NOTICE 'Original Section Name: %', original_section_name;
    RAISE NOTICE 'Class Suffix Extracted: %', class_suffix_extracted;
    RAISE NOTICE 'Generated Section ID: %', section_id;
    RAISE NOTICE '---';
    
    -- Test with display name format
    section_name := 'Life Insurance & AD&D - RTQ';
    original_section_name := 'Life Insurance & AD&D';
    class_suffix_extracted := replace(section_name, original_section_name || ' - ', '');
    section_id := lower(replace(original_section_name, ' ', '')) || '-' || lower(class_suffix_extracted);
    
    RAISE NOTICE 'Section Name: %', section_name;
    RAISE NOTICE 'Original Section Name: %', original_section_name;
    RAISE NOTICE 'Class Suffix Extracted: %', class_suffix_extracted;
    RAISE NOTICE 'Generated Section ID: %', section_id;
END $$;
