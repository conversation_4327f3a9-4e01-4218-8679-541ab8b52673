
DECLARE
    quote_record RECORD;
    carrier_deviations_map JSONB := '{}'::jsonb;
    result_pages JSONB := '[]'::jsonb;
    quote_count INTEGER := 0;
    MAX_DEVIATIONS_PER_CARRIER INTEGER := max_deviations_per_carrier;
    carrier_name TEXT;
    carrier_order_map JSONB := '{}'::jsonb;
    ordered_carriers_array JSONB := '[]'::jsonb;
    carrier_item TEXT;
    carrier_order INTEGER;
    deviations_array JSONB;
    deviation_idx INTEGER;
    total_deviations INTEGER;
    current_deviation_page JSONB;

BEGIN
    -- First, count the total number of quotes
    SELECT COUNT(DISTINCT q.quote_id) INTO quote_count
    FROM sandf.plan p
    JOIN sandf.quote q ON q.plan_id = p.plan_id
    JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
    JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
    JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
    WHERE p.plan_uuid = plan_uuid_param::uuid
    AND ec.name = 'RTQ'
    AND ecq.formatted_quote_details IS NOT NULL
    AND ecq.formatted_quote_details != '{}'::jsonb;

    -- Apply condition: if more than 4 quotes, set max deviations per carrier to 2
    IF quote_count > 4 THEN
        MAX_DEVIATIONS_PER_CARRIER := 2;
    END IF;
    -- Build carrier order map and collect deviations from all carriers
    FOR quote_record IN
        SELECT ecq.formatted_quote_details,
               c.description as carrier_description,
               c.name as carrier_name,
               q.quote_id,
               q.quote_uuid
        FROM sandf.plan p
        JOIN sandf.quote q ON q.plan_id = p.plan_id
        JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
        JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
        JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
        WHERE p.plan_uuid = plan_uuid_param::uuid
        AND ec.name = 'RTQ'
        AND ecq.formatted_quote_details IS NOT NULL
        AND ecq.formatted_quote_details != '{}'::jsonb
    LOOP
        carrier_name := quote_record.carrier_description;

        -- Get carrier order using existing function
        carrier_order := sandf.get_user_preference_order(
            user_id_param,
            plan_uuid_param,
            quote_record.quote_id,
            quote_record.quote_uuid,
            999999
        );

        -- Build carrier order map
        IF NOT carrier_order_map ? carrier_name THEN
            carrier_order_map := carrier_order_map || jsonb_build_object(
                carrier_name,
                jsonb_build_object(
                    'order', carrier_order,
                    'quote_id', quote_record.quote_id,
                    'quote_uuid', quote_record.quote_uuid
                )
            );
        END IF;

        -- Extract deviations for this carrier
        deviations_array := quote_record.formatted_quote_details -> 'deviations';

        IF deviations_array IS NOT NULL AND jsonb_typeof(deviations_array) = 'array' THEN
            -- Store deviations for this carrier
            carrier_deviations_map := carrier_deviations_map || jsonb_build_object(
                carrier_name, deviations_array
            );
        END IF;
    END LOOP;

    -- Order carriers based on user preferences
    FOR carrier_item IN
        SELECT key as carrier_name
        FROM jsonb_each(carrier_order_map)
        ORDER BY (value ->> 'order')::integer ASC, key ASC
    LOOP
        ordered_carriers_array := ordered_carriers_array || jsonb_build_array(carrier_item);
    END LOOP;



    -- Now create final pages with 2 carriers per page
    DECLARE
        page_idx INTEGER := 0;
        max_pages INTEGER;
        current_page_carriers JSONB;
        current_page_carrier_names JSONB;
        carrier_section JSONB;
        carriers_per_page INTEGER := 2;
        total_carriers INTEGER;
        carrier_idx INTEGER := 0;
        carrier_start_idx INTEGER;
        carrier_end_idx INTEGER;
        carrier_names_array TEXT[];
        current_carrier_name TEXT;
        deviation_page_idx INTEGER;
        max_deviation_pages INTEGER;
    BEGIN
        -- Convert ordered carriers to array for easier indexing
        SELECT array_agg(value::text ORDER BY ordinality)
        INTO carrier_names_array
        FROM jsonb_array_elements_text(ordered_carriers_array) WITH ORDINALITY;

        total_carriers := array_length(carrier_names_array, 1);

        -- Calculate maximum deviation pages needed across all carriers
        max_deviation_pages := 1;
        FOR carrier_idx IN 1..total_carriers LOOP
            current_carrier_name := carrier_names_array[carrier_idx];
            deviations_array := carrier_deviations_map -> current_carrier_name;
            total_deviations := jsonb_array_length(COALESCE(deviations_array, '[]'::jsonb));

            IF total_deviations > MAX_DEVIATIONS_PER_CARRIER THEN
                max_deviation_pages := GREATEST(max_deviation_pages, CEIL(total_deviations::DECIMAL / MAX_DEVIATIONS_PER_CARRIER)::INTEGER);
            END IF;
        END LOOP;

        -- Create pages by grouping carriers (2 per page) and handling deviation pagination
        FOR carrier_start_idx IN 1..total_carriers BY carriers_per_page LOOP
            carrier_end_idx := LEAST(carrier_start_idx + carriers_per_page - 1, total_carriers);

            -- For each deviation page within this carrier group
            FOR deviation_page_idx IN 0..max_deviation_pages-1 LOOP
                current_page_carriers := '[]'::jsonb;
                current_page_carrier_names := '[]'::jsonb;

                -- Process each carrier in this group (2 carriers max)
                FOR carrier_idx IN carrier_start_idx..carrier_end_idx LOOP
                    current_carrier_name := carrier_names_array[carrier_idx];
                    current_page_carrier_names := current_page_carrier_names || jsonb_build_array(current_carrier_name);

                    deviations_array := carrier_deviations_map -> current_carrier_name;

                    IF deviations_array IS NULL OR jsonb_typeof(deviations_array) != 'array' THEN
                        deviations_array := '[]'::jsonb;
                    END IF;

                    total_deviations := jsonb_array_length(deviations_array);

                    -- Get deviations for this deviation page
                    current_deviation_page := '[]'::jsonb;
                    FOR deviation_idx IN (deviation_page_idx * MAX_DEVIATIONS_PER_CARRIER)..LEAST((deviation_page_idx + 1) * MAX_DEVIATIONS_PER_CARRIER - 1, total_deviations - 1) LOOP
                        IF deviation_idx < total_deviations THEN
                            current_deviation_page := current_deviation_page || jsonb_build_array(
                                deviations_array -> deviation_idx
                            );
                        END IF;
                    END LOOP;

                    -- Add carrier section to page
                    current_page_carriers := current_page_carriers || jsonb_build_array(
                        jsonb_build_object(
                            'id', current_carrier_name,
                            'deviations', current_deviation_page
                        )
                    );
                END LOOP;

                -- Only add page if it has content (at least one carrier with deviations or first page)
                IF deviation_page_idx = 0 OR jsonb_array_length(current_page_carriers) > 0 THEN
                    -- Check if any carrier in this page has deviations
                    DECLARE
                        has_content BOOLEAN := FALSE;
                        section_check JSONB;
                    BEGIN
                        FOR section_check IN SELECT value FROM jsonb_array_elements(current_page_carriers) LOOP
                            IF jsonb_array_length(section_check -> 'deviations') > 0 THEN
                                has_content := TRUE;
                                EXIT;
                            END IF;
                        END LOOP;

                        -- Add page if it's the first deviation page or has content
                        IF deviation_page_idx = 0 OR has_content THEN
                            result_pages := result_pages || jsonb_build_array(
                                jsonb_build_object(
                                    'carriers', current_page_carrier_names,
                                    'sections', current_page_carriers
                                )
                            );
                        END IF;
                    END;
                END IF;
            END LOOP;
        END LOOP;
    END;

    -- Return array of paginated results
    RETURN result_pages;
END;
