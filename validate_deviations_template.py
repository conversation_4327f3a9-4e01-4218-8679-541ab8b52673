#!/usr/bin/env python3
"""
Simple validation script to check if the deviations data structure
is compatible with the FreeMarker template syntax.
"""

import json
import re
from typing import Dict, List, Any

def validate_freemarker_syntax(template_content: str, data: Dict[str, Any]) -> List[str]:
    """
    Basic validation of FreeMarker template syntax against data structure.
    Returns a list of potential issues.
    """
    issues = []
    
    # Check if required top-level variables exist
    required_vars = ['carriers', 'sections', 'styling']
    for var in required_vars:
        if var not in data and var != 'styling':  # styling is usually provided separately
            issues.append(f"Missing required variable: {var}")
    
    # Check carriers array
    if 'carriers' in data:
        if not isinstance(data['carriers'], list):
            issues.append("'carriers' should be an array/list")
        elif len(data['carriers']) == 0:
            issues.append("'carriers' array is empty")
    
    # Check sections structure
    if 'sections' in data:
        if not isinstance(data['sections'], list):
            issues.append("'sections' should be an array/list")
        else:
            for i, section in enumerate(data['sections']):
                if not isinstance(section, dict):
                    issues.append(f"Section {i} should be an object/dict")
                    continue
                
                if 'id' not in section:
                    issues.append(f"Section {i} missing 'id' field")
                
                if 'deviations' not in section:
                    issues.append(f"Section {i} missing 'deviations' field")
                elif not isinstance(section['deviations'], list):
                    issues.append(f"Section {i} 'deviations' should be an array/list")
    
    # Check if all carriers have corresponding sections
    if 'carriers' in data and 'sections' in data:
        carrier_ids = set(data['carriers'])
        section_ids = set(section.get('id', '') for section in data['sections'])
        
        missing_sections = carrier_ids - section_ids
        if missing_sections:
            issues.append(f"Carriers missing sections: {missing_sections}")
        
        extra_sections = section_ids - carrier_ids
        if extra_sections:
            issues.append(f"Sections without corresponding carriers: {extra_sections}")
    
    return issues

def analyze_template_variables(template_content: str) -> Dict[str, List[str]]:
    """
    Extract FreeMarker variables and expressions from template.
    """
    # Find all ${...} expressions
    variable_pattern = r'\$\{([^}]+)\}'
    variables = re.findall(variable_pattern, template_content)
    
    # Find all <#list ... as ...> expressions
    list_pattern = r'<#list\s+([^>]+)\s+as\s+([^>]+)>'
    list_expressions = re.findall(list_pattern, template_content)
    
    # Find all <#if ...> expressions
    if_pattern = r'<#if\s+([^>]+)>'
    if_expressions = re.findall(if_pattern, template_content)
    
    return {
        'variables': variables,
        'list_expressions': list_expressions,
        'if_expressions': if_expressions
    }

def main():
    print("🔍 Validating Deviations Template and Data Structure")
    print("=" * 60)
    
    # Load test data
    try:
        with open('test_deviations_data.json', 'r') as f:
            test_data = json.load(f)
        print("✅ Successfully loaded test data")
    except FileNotFoundError:
        print("❌ test_deviations_data.json not found")
        return
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON in test data: {e}")
        return
    
    # Load template
    try:
        with open('deviations.ftl', 'r') as f:
            template_content = f.read()
        print("✅ Successfully loaded deviations.ftl template")
    except FileNotFoundError:
        print("❌ deviations.ftl not found")
        return
    
    print("\n📊 Data Structure Analysis:")
    print("-" * 30)
    print(f"Carriers: {len(test_data.get('carriers', []))}")
    print(f"Sections: {len(test_data.get('sections', []))}")
    
    for i, section in enumerate(test_data.get('sections', [])):
        carrier_id = section.get('id', 'Unknown')
        deviation_count = len(section.get('deviations', []))
        print(f"  {carrier_id}: {deviation_count} deviations")
    
    print("\n🔧 Template Analysis:")
    print("-" * 30)
    template_vars = analyze_template_variables(template_content)
    
    print("Variables used:")
    for var in set(template_vars['variables']):
        print(f"  ${{{var}}}")
    
    print("\nList expressions:")
    for expr in template_vars['list_expressions']:
        print(f"  <#list {expr[0]} as {expr[1]}>")
    
    print("\nConditional expressions:")
    for expr in template_vars['if_expressions']:
        print(f"  <#if {expr}>")
    
    print("\n🧪 Validation Results:")
    print("-" * 30)
    issues = validate_freemarker_syntax(template_content, test_data)
    
    if not issues:
        print("✅ No issues found! Data structure is compatible with template.")
    else:
        print("⚠️  Issues found:")
        for issue in issues:
            print(f"  - {issue}")
    
    print("\n📋 Template Logic Summary:")
    print("-" * 30)
    print("The template will:")
    print("1. Create a table with carrier headers")
    print("2. Calculate max deviations across all carriers")
    print("3. Generate rows for each deviation index")
    print("4. Match carriers to their respective deviations")
    print("5. Display deviations in a grid format")
    
    # Calculate expected output dimensions
    max_deviations = max(len(section.get('deviations', [])) for section in test_data.get('sections', []))
    carrier_count = len(test_data.get('carriers', []))
    
    print(f"\nExpected output: {max_deviations} rows × {carrier_count} columns")
    print(f"Total cells: {max_deviations * carrier_count}")

if __name__ == "__main__":
    main()
