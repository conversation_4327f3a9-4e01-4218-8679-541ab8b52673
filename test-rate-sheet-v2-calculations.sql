-- Test script to verify rate-sheet-v2.sql calculations are working correctly
-- This script tests the calculation logic and key names

-- Step 1: First, let's check if the function compiles without errors
\echo 'Testing function compilation...'

-- Step 2: Check if we have any plans available for testing
SELECT 
    plan_uuid,
    COUNT(*) as quote_count
FROM sandf.plan p
JOIN sandf.quote q ON q.plan_id = p.plan_id
WHERE EXISTS (
    SELECT 1 FROM sandf.employee_class_quote ecq 
    WHERE ecq.quote_id = q.quote_id 
    AND ecq.formatted_quote_details IS NOT NULL
    AND ecq.formatted_quote_details != '{}'::jsonb
)
GROUP BY plan_uuid
LIMIT 5;

-- Step 3: Test the function with a sample plan (replace with actual plan UUID)
-- Uncomment and modify the following lines to test with your actual plan UUID:

/*
\echo 'Testing rate sheet v2 function...'
SELECT jsonb_pretty(
    sandf.fn_get_rate_sheet_v2(
        'your-plan-uuid-here',  -- Replace with actual plan UUID
        'your-user-id'          -- Replace with actual user ID
    )
);
*/

-- Step 4: Test calculation structure specifically
-- This query will help verify the calculation key names match the original rate-sheet.sql
/*
WITH rate_sheet_data AS (
    SELECT sandf.fn_get_rate_sheet_v2(
        'your-plan-uuid-here',
        'your-user-id'
    ) as result
),
calculations_extracted AS (
    SELECT 
        jsonb_array_elements(
            jsonb_array_elements(result) -> 'calculations'
        ) as calculation
    FROM rate_sheet_data
)
SELECT 
    calculation ->> 'carrier' as carrier,
    calculation ->> 'totalMonthlyPremiums' as monthly_premium,
    calculation ->> 'annualPremium' as annual_premium,
    calculation ->> '$ Difference From #1' as dollar_difference,
    calculation ->> 'Percentage Different From #1' as percentage_difference
FROM calculations_extracted
ORDER BY carrier;
*/

\echo 'Test script ready. Uncomment and replace plan UUID to run actual tests.'
