-- Simple verification script for section ordering in multi-class plans

-- Quick test to see section order
-- Replace 'your-plan-uuid-here' with your actual multi-class plan UUID
WITH section_data AS (
    SELECT 
        ROW_NUMBER() OVER () as position,
        section_info ->> 'id' as section_id,
        section_info ->> 'name' as section_name,
        -- Extract base section name (without class suffix)
        regexp_replace(section_info ->> 'name', ' - [0-9]+[A-Za-z]+$', '') as base_section,
        -- Extract class suffix
        CASE 
            WHEN section_info ->> 'name' ~ ' - [0-9]+[A-Za-z]+$' THEN
                regexp_replace(section_info ->> 'name', '^.* - ([0-9]+[A-Za-z]+)$', '\1')
            ELSE 
                'single'
        END as class_suffix
    FROM (
        SELECT jsonb_array_elements(
            (sandf.fn_get_plan_design_report_multi_v2(
                'your-plan-uuid-here',  -- Replace with your plan UUID
                'your-user-id',         -- Replace with your user ID
                NULL,
                NULL
            )::jsonb -> 0 -> 'sections')
        ) as section_info
    ) sections
)
SELECT 
    position,
    section_name,
    base_section,
    class_suffix,
    CASE 
        WHEN LAG(base_section) OVER (ORDER BY position) = base_section THEN '  ↳ Same section, different class'
        ELSE '● New section group'
    END as grouping_note
FROM section_data
ORDER BY position;

-- Expected output should show sections grouped by base section:
-- 1  | Life Insurance & AD&D - 1RTQ        | Life Insurance & AD&D | 1RTQ        | ● New section group
-- 2  | Life Insurance & AD&D - 2Management | Life Insurance & AD&D | 2Management |   ↳ Same section, different class  
-- 3  | Dependent Life - 1RTQ               | Dependent Life        | 1RTQ        | ● New section group
-- 4  | Dependent Life - 2Management        | Dependent Life        | 2Management |   ↳ Same section, different class
-- etc.

-- If sections are NOT properly grouped, you'll see mixed ordering like:
-- 1  | Dependent Life - 1RTQ               | Dependent Life        | 1RTQ        | ● New section group
-- 2  | Life Insurance & AD&D - 1RTQ        | Life Insurance & AD&D | 1RTQ        | ● New section group  
-- 3  | Dependent Life - 2Management        | Dependent Life        | 2Management | ● New section group
-- 4  | Life Insurance & AD&D - 2Management | Life Insurance & AD&D | 2Management | ● New section group

-- Quick check for employee classes in your plan
SELECT 
    'Employee Classes in Plan:' as info,
    COUNT(DISTINCT ec.name) as total_classes,
    string_agg(DISTINCT ec.name, ', ' ORDER BY ec.name) as class_names
FROM sandf.plan p
JOIN sandf.quote q ON q.plan_id = p.plan_id
JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
WHERE p.plan_uuid = 'your-plan-uuid-here'::uuid
AND ecq.formatted_quote_details IS NOT NULL
AND ecq.formatted_quote_details != '{}'::jsonb;
