-- Test script for the new multi-class plan design function

-- First, check what employee classes exist for your plan
SELECT 
    p.plan_uuid,
    COUNT(DISTINCT ec.name) as total_employee_classes,
    array_agg(DISTINCT ec.name ORDER BY ec.name) as employee_class_names
FROM sandf.plan p
JOIN sandf.quote q ON q.plan_id = p.plan_id
JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
WHERE p.plan_uuid = 'your-plan-uuid-here'::uuid
AND ecq.formatted_quote_details IS NOT NULL
AND ecq.formatted_quote_details != '{}'::jsonb
GROUP BY p.plan_uuid;

-- Test the new function
SELECT jsonb_pretty(
    fn_get_plan_design_report_v2(
        'your-plan-uuid-here',  -- Replace with your actual plan UUID
        'your-user-id',         -- Replace with your user ID (optional)
        NULL,                   -- includes_param (optional)
        NULL                    -- excludes_param (optional)
    )
);

-- Example with specific includes/excludes
SELECT jsonb_pretty(
    fn_get_plan_design_report_v2(
        'your-plan-uuid-here',
        'your-user-id',
        ARRAY['groupName1', 'groupName2'],  -- Only include these groups
        ARRAY['excludeGroup']               -- Exclude these groups
    )
);

-- Compare with old function (single class only)
SELECT jsonb_pretty(
    fn_get_plan_design_report(
        'your-plan-uuid-here',
        'your-user-id',
        NULL,
        NULL
    )
);
