-- Test script for the new multi-class rate sheet function v2
-- This script tests both single-class and multi-class scenarios with pagination

-- Step 1: Check what employee classes exist for your plan
-- Replace 'your-plan-uuid-here' with your actual plan UUID
SELECT 
    p.plan_uuid,
    COUNT(DISTINCT ec.name) as total_employee_classes,
    array_agg(DISTINCT ec.name ORDER BY ec.name) as employee_class_names
FROM sandf.plan p
JOIN sandf.quote q ON q.plan_id = p.plan_id
JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
WHERE p.plan_uuid = 'your-plan-uuid-here'::uuid
AND ecq.formatted_quote_details IS NOT NULL
AND ecq.formatted_quote_details != '{}'::jsonb
GROUP BY p.plan_uuid;

-- Step 2: Test the new v2 function
-- This will automatically detect if it's single or multi-class and handle accordingly
SELECT jsonb_pretty(
    sandf.fn_get_rate_sheet_v2(
        'your-plan-uuid-here',  -- Replace with your actual plan UUID
        'your-user-id'          -- Replace with your user ID (optional)
    )
);

-- Step 3: Compare with old function (for reference)
-- This helps verify that the data structure is consistent
SELECT 'OLD FUNCTION OUTPUT (for comparison):' as comparison;
SELECT jsonb_pretty(
    sandf.fn_get_rate_sheet(
        'your-plan-uuid-here',
        'your-user-id'
    )
);

-- Step 4: Test pagination by checking the structure
-- This query helps you understand the pagination structure
WITH rate_sheet_data AS (
    SELECT sandf.fn_get_rate_sheet_v2(
        'your-plan-uuid-here',
        'your-user-id'
    ) as result
),
pages AS (
    SELECT 
        generate_subscripts(ARRAY(SELECT jsonb_array_elements(result)), 1) as page_number,
        jsonb_array_elements(result) as page_data
    FROM rate_sheet_data
)
SELECT 
    page_number,
    jsonb_array_length(page_data -> 'calculations') as calculations_count,
    page_data -> 'carriers' as carriers,
    jsonb_pretty(page_data -> 'calculations') as calculations_preview
FROM pages
ORDER BY page_number;

-- Step 5: Verify employee class handling
-- This query shows how different employee classes are processed
WITH rate_sheet_data AS (
    SELECT sandf.fn_get_rate_sheet_v2(
        'your-plan-uuid-here',
        'your-user-id'
    ) as result
),
all_calculations AS (
    SELECT 
        jsonb_array_elements(
            jsonb_array_elements(result) -> 'calculations'
        ) as calculation
    FROM rate_sheet_data
)
SELECT 
    calculation ->> 'employeeClass' as employee_class,
    calculation ->> 'carrier' as carrier,
    calculation ->> 'calculationName' as calculation_name,
    (calculation ->> 'monthlyPremium')::numeric as monthly_premium,
    (calculation ->> 'annualPremium')::numeric as annual_premium
FROM all_calculations
ORDER BY employee_class, carrier;

-- Step 6: Test global employee class resolver (optional)
-- This shows what the global resolver returns for your plan
SELECT * FROM sandf.fn_resolve_employee_class_global('your-plan-uuid-here');

-- Step 7: Performance test (optional)
-- Uncomment to test performance with timing
/*
\timing on
SELECT 'Performance test - Rate Sheet v2:' as test_name;
SELECT sandf.fn_get_rate_sheet_v2('your-plan-uuid-here', 'your-user-id');
\timing off
*/
