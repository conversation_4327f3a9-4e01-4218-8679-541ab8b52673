-- Test script to verify carrier ordering consistency across employee classes

-- Step 1: Check carrier ordering for a multi-class plan
-- Replace 'your-plan-uuid-here' with your actual plan UUID
SELECT 
    p.plan_uuid,
    ec.name as employee_class_name,
    c.description as carrier_description,
    sandf.get_user_preference_order(
        'your-user-id',  -- Replace with your user ID
        p.plan_uuid::text,
        q.quote_id,
        q.quote_uuid,
        999999
    ) as carrier_order,
    q.quote_id,
    q.quote_uuid
FROM sandf.plan p
JOIN sandf.quote q ON q.plan_id = p.plan_id
JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
WHERE p.plan_uuid = 'your-plan-uuid-here'::uuid
AND ecq.formatted_quote_details IS NOT NULL
AND ecq.formatted_quote_details != '{}'::jsonb
ORDER BY ec.name, 
         sandf.get_user_preference_order(
             'your-user-id',
             p.plan_uuid::text,
             q.quote_id,
             q.quote_uuid,
             999999
         ) ASC,
         c.description ASC;

-- Step 2: Test the fixed function to verify consistent carrier ordering
SELECT jsonb_pretty(
    sandf.fn_get_plan_design_report_multi_v2(
        'your-plan-uuid-here',  -- Replace with your actual plan UUID
        'your-user-id',         -- Replace with your user ID
        NULL,                   -- includes_param
        NULL                    -- excludes_param
    )
);

-- Step 3: Extract just the carriers array to verify ordering
SELECT 
    page_index,
    jsonb_pretty(page_data -> 'carriers') as carriers_order
FROM (
    SELECT 
        generate_subscripts(result_array, 1) as page_index,
        result_array[generate_subscripts(result_array, 1)] as page_data
    FROM (
        SELECT 
            ARRAY(SELECT jsonb_array_elements(
                sandf.fn_get_plan_design_report_multi_v2(
                    'your-plan-uuid-here',
                    'your-user-id',
                    NULL,
                    NULL
                )::jsonb
            )) as result_array
    ) sub
) pages;

-- Step 4: Verify section naming for multi-class
SELECT 
    page_index,
    section_index,
    section_data ->> 'id' as section_id,
    section_data ->> 'name' as section_name,
    jsonb_array_length(section_data -> 'benefits') as benefit_count
FROM (
    SELECT 
        generate_subscripts(result_array, 1) as page_index,
        result_array[generate_subscripts(result_array, 1)] as page_data
    FROM (
        SELECT 
            ARRAY(SELECT jsonb_array_elements(
                sandf.fn_get_plan_design_report_multi_v2(
                    'your-plan-uuid-here',
                    'your-user-id',
                    NULL,
                    NULL
                )::jsonb
            )) as result_array
    ) sub
) pages,
LATERAL (
    SELECT 
        generate_subscripts(sections_array, 1) as section_index,
        sections_array[generate_subscripts(sections_array, 1)] as section_data
    FROM (
        SELECT ARRAY(SELECT jsonb_array_elements(page_data -> 'sections')) as sections_array
    ) sections
) section_details
ORDER BY page_index, section_index;

-- Expected Results:
-- 1. Carrier ordering should be consistent across all employee classes
-- 2. Section IDs should have class suffixes for multi-class (e.g., "lifeinsuranceadad-1rtq")
-- 3. Section names should have class suffixes (e.g., "Life Insurance & AD&D - 1RTQ")
-- 4. All pages should have the same carrier order

-- Notes:
-- - The fix ensures that carrier_order_map is built once and used consistently
-- - Each employee class uses the same carrier ordering from the first pass
-- - This prevents different ordering per employee class that was causing the issue
