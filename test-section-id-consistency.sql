-- Test script to verify section ID consistency fix
-- This tests that section IDs generated for benefits match section IDs generated for sections

DO $$
DECLARE
    -- Test data simulating the issue
    group_name TEXT;
    benefit_key TEXT;
    class_suffix TEXT;
    original_section_name TEXT;
    class_suffix_extracted TEXT;
    
    -- Generated IDs
    benefit_section_id TEXT;
    section_id TEXT;
    
    -- Test cases
    test_cases TEXT[][] := ARRAY[
        ['Life Insurance & AD&D', 'lifeInsuranceADAD', 'Class 1 - Salaried EE''s'],
        ['Dependent Life', 'dependentLife', 'Class 2 - Hourly EE''s'],
        ['Extended Health', 'extendedHealth', 'RTQ'],
        ['Dental', 'dental', 'Management']
    ];
    
    test_case TEXT[];
    i INTEGER;
BEGIN
    RAISE NOTICE 'Testing Section ID Consistency Fix';
    RAISE NOTICE '=====================================';
    
    FOR i IN 1..array_length(test_cases, 1) LOOP
        test_case := test_cases[i];
        group_name := test_case[1];
        benefit_key := test_case[2];
        class_suffix := test_case[3];
        original_section_name := test_case[1];
        class_suffix_extracted := class_suffix;
        
        -- Generate benefit section ID (how it's done in benefit creation)
        benefit_section_id := lower(replace(replace(replace(group_name, ' ', ''), '&', ''), '''', '')) || '-' || lower(replace(replace(trim(class_suffix), ' ', ''), '''', ''));
        
        -- Generate section ID (how it's done in section creation)
        section_id := lower(replace(replace(replace(original_section_name, ' ', ''), '&', ''), '''', '')) || '-' || lower(replace(replace(trim(class_suffix_extracted), ' ', ''), '''', ''));
        
        RAISE NOTICE 'Test Case %:', i;
        RAISE NOTICE '  Group Name: %', group_name;
        RAISE NOTICE '  Class Suffix: %', class_suffix;
        RAISE NOTICE '  Benefit Section ID: %', benefit_section_id;
        RAISE NOTICE '  Section ID: %', section_id;
        RAISE NOTICE '  Match: %', CASE WHEN benefit_section_id = section_id THEN 'YES' ELSE 'NO' END;
        RAISE NOTICE '';
    END LOOP;
    
    RAISE NOTICE 'Test completed. All section IDs should match for consistency.';
END $$;
