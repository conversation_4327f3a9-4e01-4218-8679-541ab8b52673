-- Test script for the consolidated multi-class plan design function

-- First, check what employee classes exist for your plan
SELECT 
    p.plan_uuid,
    COUNT(DISTINCT ec.name) as total_employee_classes,
    array_agg(DISTINCT ec.name ORDER BY ec.name) as employee_class_names
FROM sandf.plan p
JOIN sandf.quote q ON q.plan_id = p.plan_id
JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
WHERE p.plan_uuid = 'your-plan-uuid-here'::uuid
AND ecq.formatted_quote_details IS NOT NULL
AND ecq.formatted_quote_details != '{}'::jsonb
GROUP BY p.plan_uuid;

-- Test the consolidated multi-class function
SELECT jsonb_pretty(
    fn_get_plan_design_report_v2(
        'your-plan-uuid-here',  -- Replace with your actual plan UUID
        'your-user-id',         -- Replace with your user ID (optional)
        NULL,                   -- includes_param (optional)
        NULL                    -- excludes_param (optional)
    )
);

-- Expected output structure for multi-class:
-- [
--   {
--     "carriers": ["Carrier A", "Carrier B", "Carrier C"],
--     "sections": [
--       {
--         "id": "lifeinsuranceadad-class1",
--         "name": "Life Insurance & AD&D - Class1",
--         "benefits": [...]
--       },
--       {
--         "id": "lifeinsuranceadad-class2", 
--         "name": "Life Insurance & AD&D - Class2",
--         "benefits": [...]
--       },
--       {
--         "id": "dependentlife-class1",
--         "name": "Dependent Life - Class1", 
--         "benefits": [...]
--       },
--       {
--         "id": "dependentlife-class2",
--         "name": "Dependent Life - Class2",
--         "benefits": [...]
--       }
--     ]
--   }
-- ]

-- Compare with old function output structure
SELECT 'OLD FUNCTION OUTPUT:' as comparison;
SELECT jsonb_pretty(
    fn_get_plan_design_report(
        'your-plan-uuid-here',
        'your-user-id',
        NULL,
        NULL
    )
);

SELECT 'NEW CONSOLIDATED FUNCTION OUTPUT:' as comparison;
SELECT jsonb_pretty(
    fn_get_plan_design_report_v2(
        'your-plan-uuid-here',
        'your-user-id',
        NULL,
        NULL
    )
);
