-- Deployment script for the new multi-class rate sheet function v2
-- Execute this script to create the new function in your database

-- Step 1: Create the function
-- This will read from the rate-sheet-v2.sql file
\i multi-class/rate-sheet-v2.sql

-- Step 2: Verify the function was created successfully
SELECT 
    routine_name,
    routine_type,
    data_type,
    routine_definition IS NOT NULL as has_definition
FROM information_schema.routines 
WHERE routine_name = 'fn_get_rate_sheet_v2'
AND routine_schema = 'sandf';

-- Step 3: Test with a sample plan (replace with your actual plan UUID)
-- Uncomment and modify the following lines to test:

/*
-- Check employee classes for your plan
SELECT 
    p.plan_uuid,
    COUNT(DISTINCT ec.name) as total_employee_classes,
    array_agg(DISTINCT ec.name ORDER BY ec.name) as employee_class_names
FROM sandf.plan p
JOIN sandf.quote q ON q.plan_id = p.plan_id
JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
WHERE p.plan_uuid = 'your-plan-uuid-here'::uuid
AND ecq.formatted_quote_details IS NOT NULL
AND ecq.formatted_quote_details != '{}'::jsonb
GROUP BY p.plan_uuid;

-- Test the new function
SELECT jsonb_pretty(
    sandf.fn_get_rate_sheet_v2(
        'your-plan-uuid-here',
        'your-user-id'
    )
);

-- Test pagination structure
WITH rate_sheet_data AS (
    SELECT sandf.fn_get_rate_sheet_v2(
        'your-plan-uuid-here',
        'your-user-id'
    ) as result
),
pages AS (
    SELECT 
        generate_subscripts(ARRAY(SELECT jsonb_array_elements(result)), 1) as page_number,
        jsonb_array_elements(result) as page_data
    FROM rate_sheet_data
)
SELECT 
    page_number,
    jsonb_array_length(page_data -> 'calculations') as calculations_per_page,
    array_length(ARRAY(SELECT jsonb_array_elements_text(page_data -> 'carriers')), 1) as carriers_count
FROM pages
ORDER BY page_number;
*/

-- Step 4: Grant necessary permissions (if needed)
-- Uncomment if you need to grant permissions to specific roles
/*
GRANT EXECUTE ON FUNCTION sandf.fn_get_rate_sheet_v2(TEXT, TEXT) TO your_role_name;
*/

-- Step 5: Create a wrapper function for backward compatibility (optional)
-- This allows existing code to use the new function with minimal changes
/*
CREATE OR REPLACE FUNCTION sandf.fn_get_rate_sheet_multi_class(
    plan_uuid_param TEXT,
    user_id_param TEXT DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
BEGIN
    -- Simply call the v2 function
    RETURN sandf.fn_get_rate_sheet_v2(plan_uuid_param, user_id_param);
END;
$$;
*/
