-- Deployment script for the new plan design multi-class function with groupby logic
-- This script deploys the updated function that groups sections by matching benefits

-- First, backup the original function (optional)
-- CREATE OR REPLACE FUNCTION sandf.fn_get_plan_design_report_multi_class_original AS 
-- (copy of original function for backup)

-- Deploy the new function with groupby logic
\i multi-class/mutli-class.sql

-- Verify the function was created successfully
SELECT 
    routine_name,
    routine_type,
    data_type
FROM information_schema.routines 
WHERE routine_schema = 'sandf' 
AND routine_name = 'fn_get_plan_design_report_multi_class';

-- Test with a sample plan (replace with actual plan UUID)
/*
-- Default pagination (10 benefits per page, same as original)
SELECT jsonb_pretty(
    sandf.fn_get_plan_design_report_multi_class(
        'your-plan-uuid-here',
        'your-user-id',
        NULL,
        NULL
    )
);
*/

/*
Key changes in the new function:

1. GROUPBY LOGIC:
   - Added class-to-letter mapping (A, B, C...) for groupby logic
   - Collects section data by class first, then groups by matching benefit values
   - Creates combined sections with appropriate suffixes:
     * "ALL" when benefits match across all classes
     * "A", "B", "C" when benefits differ between classes
   - Maintains proper ordering: ALL first, then A, B, C, then combinations
   - Follows the same pattern as rate-sheet-v2.sql for consistency

2. ENHANCED PAGINATION (Based on original multi-class-old.sql):
   - Uses same pagination logic as original: MAX_BENEFITS_PER_PAGE = 10
   - Pagination is based on benefit count, not section count
   - ENHANCED: Sections are NEVER split across multiple page objects
   - Before adding a section, checks if entire section fits in current page
   - If section would exceed page limit, creates new page first
   - Each section remains complete within one page object

3. FUNCTION SIGNATURE (Same as original):
   fn_get_plan_design_report_multi_class(
       plan_uuid_param TEXT,
       user_id_param TEXT DEFAULT NULL,
       includes_param TEXT[] DEFAULT NULL,
       excludes_param TEXT[] DEFAULT NULL
   )

4. OUTPUT FORMAT CHANGES:
   - Old: "Life Insurance & AD&D - Class 1 - Salaried EE's"
   - New: "Life Insurance & AD&D A" (if benefits differ) or "Life Insurance & AD&D ALL" (if benefits match)

5. PAGINATION BEHAVIOR:
   - Each page object contains: {"carriers": [...], "sections": [...]}
   - Sections array contains complete sections (never partial)
   - Carriers array is included on every page for consistency
   - If total benefits ≤ 10, returns single page object
   - If total benefits > 10, creates multiple pages but keeps sections intact
   - Example: Section with 12 benefits gets its own page (won't be split)

This provides the plan-design-new.json format you requested with smart pagination.
*/
