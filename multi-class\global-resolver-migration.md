# Global Employee Class Resolver Migration Guide

## Overview
Created centralized employee class resolution functions in `global.sql` that handle the logic for choosing between RTQ (single class) and multi-class (first object) approaches.

## New Global Functions in `global.sql`

### 1. `sandf.fn_resolve_employee_class_global(plan_uuid)`
**Returns complete information:**
- `employee_class_name`: The resolved employee class name
- `employee_class_id`: The corresponding ID  
- `employee_class_count`: Total number of employee classes
- `all_employee_classes`: Array of all available employee classes
- `is_rtq_only`: <PERSON><PERSON><PERSON> indicating if this is single RTQ scenario
- `resolution_strategy`: 'SINGLE_RTQ' or 'MULTI_CLASS_FIRST_OBJECT'

### 2. `sandf.fn_get_resolved_employee_class(plan_uuid)` ⭐ **Most Used**
**Returns just the employee class name** (simplest to use)

### 3. `sandf.fn_get_resolved_employee_class_id(plan_uuid)`
**Returns just the employee class ID** (for functions that need the ID)

## Resolution Logic

```
IF employee_class_count = 1 AND employee_classes[1] = 'RTQ' THEN
    → Use 'RTQ' (single RTQ scenario)
ELSE
    → Use first_employee_class (multi-class scenario)
```

## Migration Pattern

### ❌ Before (15+ lines of duplicated logic):
```sql
DECLARE
    employee_classes TEXT[];
    employee_class_count INTEGER;
    first_employee_class TEXT;
BEGIN
    -- Get all employee classes and use the first one dynamically
    SELECT COUNT(DISTINCT ec.name), array_agg(DISTINCT ec.name ORDER BY ec.name)
    INTO employee_class_count, employee_classes
    FROM sandf.plan p
    JOIN sandf.quote q ON q.plan_id = p.plan_id
    JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
    JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
    WHERE p.plan_uuid = plan_uuid_param::uuid
    AND ecq.formatted_quote_details IS NOT NULL
    AND ecq.formatted_quote_details != '{}'::jsonb;

    first_employee_class := employee_classes[1];
    
    -- Use in queries...
    WHERE ec.name = first_employee_class
```

### ✅ After (2 lines):
```sql
DECLARE
    resolved_employee_class TEXT;
BEGIN
    -- Use global resolver - handles both RTQ and multi-class automatically
    resolved_employee_class := sandf.fn_get_resolved_employee_class(plan_uuid_param);
    
    -- Use in queries...
    WHERE ec.name = resolved_employee_class
```

## Files to Update

### ✅ Completed:
1. `devation.sql` - Updated as example

### 🔄 Need to Update:
2. `otherConsideration.sql`
3. `rate-sheet.sql` 
4. `rate-pre-ee.sql`
5. `renewal-target.sql`
6. `cutting-chase.sql`

## Step-by-Step Migration for Each File

### Step 1: Update DECLARE section
**Remove:**
```sql
employee_classes TEXT[];
employee_class_count INTEGER;
first_employee_class TEXT;
```

**Add:**
```sql
resolved_employee_class TEXT;
```

### Step 2: Update BEGIN section
**Replace the entire employee class detection block with:**
```sql
resolved_employee_class := sandf.fn_get_resolved_employee_class(plan_uuid_param);
```

### Step 3: Update all WHERE clauses
**Replace all instances of:**
- `ec.name = first_employee_class` → `ec.name = resolved_employee_class`
- `ec.name = rtq_name` → `ec.name = resolved_employee_class`
- `AND rtq_set_flag = TRUE` → `AND name = resolved_employee_class`

## Special Cases

### For functions that need employee_class_id:
```sql
resolved_employee_class_id := sandf.fn_get_resolved_employee_class_id(plan_uuid_param);
```

### For functions that need full information:
```sql
SELECT employee_class_name, employee_class_id, employee_class_count, is_rtq_only
INTO resolved_name, resolved_id, class_count, is_single_rtq
FROM sandf.fn_resolve_employee_class_global(plan_uuid_param);
```

## Benefits

✅ **Single Point of Control** - Change logic once, affects all functions  
✅ **Optimized Logic** - Handles both RTQ and multi-class scenarios intelligently  
✅ **No Duplication** - 15+ lines reduced to 1 line per file  
✅ **Consistent Behavior** - All functions use same resolution strategy  
✅ **Easy Maintenance** - Modify global.sql to change behavior everywhere  
✅ **Clear Strategy** - Logs show which approach was used  

## Testing

After migration, you can verify the resolution strategy by checking the logs:
- `"Employee class resolution: Using single RTQ class"` - Single RTQ scenario
- `"Employee class resolution: Using first employee class (X) from Y total classes"` - Multi-class scenario

This approach gives you the best of both worlds - optimized for RTQ when appropriate, but falls back to multi-class first object when needed!
