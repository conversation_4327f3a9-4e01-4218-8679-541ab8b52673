# Dynamic Plan Design Function - Multi-Class Support

## Overview

The new `fn_get_plan_design_report_v2` function removes the hardcoded 'RTQ' employee class dependency and dynamically processes all employee classes for a given plan using loops, as requested.

## Key Changes from Original Function

### 1. Removed Hardcoded Employee Class
- **Old**: `AND ec.name = 'RTQ'` (hardcoded)
- **New**: Dynamic detection of all employee classes for the plan

### 2. Added Employee Class Detection
```sql
SELECT COUNT(DISTINCT ec.name), array_agg(DISTINCT ec.name ORDER BY ec.name)
INTO employee_class_count, employee_classes
FROM sandf.plan p
JOIN sandf.quote q ON q.plan_id = p.plan_id
JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
WHERE p.plan_uuid = plan_uuid_param::uuid
```

### 3. Implemented Loop-Based Processing
- **Single Class**: Uses optimized original logic for performance
- **Multiple Classes**: Uses `FOREACH` loop to process each employee class

```sql
FOREACH current_employee_class IN ARRAY employee_classes
LOOP
    -- Process each employee class separately
    -- Apply class suffix to section names and IDs
END LOOP;
```

### 4. Dynamic Section Naming for Multi-Class
- Section IDs: `{original_section_id}-{class_index}{class_name}`
- Section Names: `{original_section_name} - {class_index}{class_name}`
- Examples: 
  - `lifeinsuranceadad-1RTQ`
  - `Life Insurance & AD&D - 1RTQ`
  - `dependentlife-2Management`
  - `Dependent Life - 2Management`

## Function Logic Flow

### Step 1: Employee Class Detection
Automatically detects all employee classes for the plan without hardcoding.

### Step 2: Conditional Processing
- **If 1 employee class**: Uses original optimized logic (no class suffixes)
- **If multiple classes**: Uses loop-based multi-class logic

### Step 3: Loop Processing (Multi-Class)
```sql
class_index := 1;
FOREACH current_employee_class IN ARRAY employee_classes
LOOP
    -- Create class suffix: class_index + class_name
    class_suffix := class_index::TEXT || current_employee_class;
    
    -- Process quotes for this specific employee class
    FOR quote_record IN
        SELECT ... WHERE ec.name = current_employee_class
    LOOP
        -- Process plan details with class suffix
    END LOOP;
    
    class_index := class_index + 1;
END LOOP;
```

### Step 4: Unified Output
All employee classes are consolidated into a single output structure with proper section naming.

## Benefits

1. **No Hardcoded Dependencies**: Works with any employee class names
2. **Dynamic Scaling**: Automatically handles any number of employee classes
3. **Performance Optimized**: Single-class plans use original fast logic
4. **Backward Compatible**: Single-class behavior identical to original
5. **Loop-Based**: Uses explicit loops as requested for multi-class processing
6. **Consistent Structure**: Maintains same output format and pagination

## Usage Examples

### Single Employee Class Plan
```sql
SELECT fn_get_plan_design_report_v2('plan-uuid', 'user-id');
-- Output: Same as original function (no class suffixes)
```

### Multi Employee Class Plan
```sql
SELECT fn_get_plan_design_report_v2('plan-uuid', 'user-id');
-- Output: Sections with class suffixes like "1RTQ", "2Management"
```

## Migration Guide

### From Old Function
```sql
-- Old (hardcoded RTQ)
SELECT fn_get_plan_design_report('plan-uuid', 'user-id');

-- New (dynamic all classes)
SELECT fn_get_plan_design_report_v2('plan-uuid', 'user-id');
```

### Key Differences
1. **Dynamic**: No hardcoded employee class names
2. **Loop-Based**: Uses explicit loops for multi-class processing
3. **Scalable**: Handles any number of employee classes
4. **Consistent**: Same output structure with class-aware naming

## Files Created

1. `multi-class/multi-class-support/plan-design-v2.sql`: Main dynamic function
2. `test-dynamic-plan-design.sql`: Test script
3. `deploy-dynamic-plan-design.sql`: Deployment script
4. `dynamic-plan-design-summary.md`: This documentation

## Testing

Use the provided test script to verify the function works with your data:

```sql
-- Check employee classes
SELECT COUNT(DISTINCT ec.name), array_agg(DISTINCT ec.name)
FROM sandf.plan p
JOIN sandf.quote q ON q.plan_id = p.plan_id
JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
WHERE p.plan_uuid = 'your-plan-uuid'::uuid;

-- Test the function
SELECT fn_get_plan_design_report_v2('your-plan-uuid', 'your-user-id');
```

## Next Steps

1. Execute `deploy-dynamic-plan-design.sql` to create the function
2. Test with your actual plan UUIDs using `test-dynamic-plan-design.sql`
3. Update your application to use the new dynamic function
4. Verify output format matches your requirements for both single and multi-class scenarios
