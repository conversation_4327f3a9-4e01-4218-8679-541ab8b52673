# Renewal Target Multi-Class Support Changes

## Overview
Updated `renewal-target.sql` to support multi-class employee scenarios following the same pattern used in `rate-sheet-v2.sql`. The function now dynamically processes all employee classes and calculates renewal targets for each class across all coverage types (single, couple, family).

## Key Changes Made

### 1. Variable Declarations
- **Added multi-class support variables:**
  - `employee_classes TEXT[]` - Array of all employee class names
  - `employee_class_count INTEGER` - Count of employee classes
  - `current_employee_class TEXT` - Current class being processed
  - `class_suffix TEXT` - Class name suffix for identification
  - `class_number TEXT` - Extracted class number

- **Added carrier ordering support:**
  - `carrier_order INTEGER` - User preference order for carriers
  - `carrier_order_map JSONB` - Map of carrier orders
  - `ordered_carriers_array TEXT[]` - Ordered array of carriers

- **Added coverage type processing:**
  - `coverage_order TEXT[]` - Array for single/couple/family processing
  - `coverage_type TEXT` - Current coverage type being processed
  - `subval JSONB` - Subvalue for nested structures

### 2. Employee Class Detection
- **Replaced single class resolution** with dynamic detection:
  ```sql
  SELECT COUNT(DISTINCT ec.name), array_agg(DISTINCT ec.name ORDER BY ec.name)
  INTO employee_class_count, employee_classes
  FROM sandf.plan p
  JOIN sandf.quote q ON q.plan_id = p.plan_id
  JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
  JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
  WHERE p.plan_uuid = target_plan_uuid::uuid
  ```

### 3. Carrier Order Management
- **Added carrier preference ordering** following rate-sheet-v2 pattern:
  - Builds `carrier_order_map` with user preferences
  - Creates `ordered_carriers_array` for consistent ordering
  - Uses `sandf.get_user_preference_order()` function

### 4. Multi-Class Processing Loop
- **Replaced single employee class processing** with:
  ```sql
  FOREACH current_employee_class IN ARRAY employee_classes
  LOOP
      -- Extract class number for identification
      class_number := CASE
          WHEN current_employee_class ~ 'Class [0-9]+' THEN
              regexp_replace(current_employee_class, '.*Class ([0-9]+).*', '\1')
          ELSE
              '1'
      END;
      
      -- Process all quotes for this employee class
      FOR quote_record IN ...
  ```

### 5. Enhanced Premium Calculation
- **Added support for single/couple/family structures:**
  - Detects if premium data has nested structure (single/couple/family)
  - Processes each coverage type separately
  - Falls back to legacy direct structure if needed
  - Applies same logic to both Extended Health and Dental premiums

- **Extended Health Premium Processing:**
  ```sql
  IF quote_json #> '{benefitPremiums,extendedHealthPremium}' ? 'single' OR 
     quote_json #> '{benefitPremiums,extendedHealthPremium}' ? 'couple' OR 
     quote_json #> '{benefitPremiums,extendedHealthPremium}' ? 'family' THEN
      -- Handle nested structure
      FOREACH coverage_type IN ARRAY coverage_order
      LOOP
          -- Process single, couple, family separately
      END LOOP;
  ELSE
      -- Handle legacy direct structure
  END IF;
  ```

### 6. Rating Factor Processing
- **Maintained existing rating factor logic** for:
  - `trend_ehc` (Extended Health pooling)
  - `ibnr_ehc` (Extended Health IBNR)
  - `tlr_ehc` (Extended Health Target Loss Ratio)
  - `ibnr_dental` (Dental IBNR)
  - `tlr_dental` (Dental Target Loss Ratio)

- **Same calculation formula preserved:**
  ```sql
  amount := (ehc_avg * (1 - (trend_ehc / 100)) * (1 - (ibnr_ehc / 100)) * (1 - (tlr_ehc / 100))) +
           (dental_avg * (1 - (ibnr_dental / 100)) * (1 - (tlr_dental / 100)));
  ```

### 7. Result Structure
- **Updated return structure** to use ordered carriers:
  ```sql
  RETURN jsonb_build_object(
      'targetedClaims', jsonb_build_object(
          'carriers', ordered_carriers_array,
          'sections', section_list
      ),
      'renewalCharges', jsonb_build_object(
          'carriers', ordered_carriers_array,
          'sections', renewal_section_list
      )
  );
  ```

## Benefits of Multi-Class Support

1. **Dynamic Processing**: Automatically detects and processes all employee classes
2. **Coverage Type Support**: Handles single, couple, family premium structures
3. **Consistent Ordering**: Uses user preference ordering for carriers
4. **Backward Compatibility**: Maintains support for legacy direct premium structures
5. **Scalable**: Works with any number of employee classes
6. **Same Calculations**: Preserves existing renewal target calculation logic

## Usage
The function maintains the same signature and can be called exactly as before:
```sql
SELECT sandf.fn_get_renewal_target(plan_uuid, user_id);
```

The function will automatically detect whether the plan has single or multiple employee classes and process accordingly.
