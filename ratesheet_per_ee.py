import json


def create_rate_sheet_per_ee(rateSheet, number_of_employees):
    """
    Create a rate sheet per employee for the first part of the project.
    This function initializes the rate sheet with the necessary attributes.
    """
    rate_sheet_ee = {}

    for carrier, carrier_data in rateSheet["carriers"].items():
        rate_sheet_ee[carrier] = {}
        rate_sheet_ee[carrier]["Single"] = 0
        rate_sheet_ee[carrier]["Single_add"] = 0
        for each_benefit in carrier_data["benefits"]:
            if each_benefit["name"].endswith("Single"):
                rate_sheet_ee[carrier]["Single"] += each_benefit["rate"]
            elif each_benefit["name"] in ["Accidental Death & Dismemberment", "Life insurance"]:
                rate_sheet_ee[carrier]["Single_add"] += float(each_benefit["premium"])
        single_add = rate_sheet_ee[carrier].pop("Single_add")/number_of_employees
        rate_sheet_ee[carrier]["Single"] += single_add

    for carrier, carrier_data in rateSheet["carriers"].items():
        rate_sheet_ee[carrier]["Family"] = 0
        rate_sheet_ee[carrier]["Family_add"] = 0
        for each_benefit in carrier_data["benefits"]:
            if each_benefit["name"].endswith("Family"):
                rate_sheet_ee[carrier]["Family"] += each_benefit["rate"]
            elif each_benefit["name"] in ["Accidental Death & Dismemberment", "Life insurance"]:
                rate_sheet_ee[carrier]["Family_add"] += float(each_benefit["premium"])
        family_add = rate_sheet_ee[carrier].pop("Family_add")/number_of_employees
        rate_sheet_ee[carrier]["Family"] += family_add

    return rate_sheet_ee


if __name__ == "__main__":
    # Example usage
    with open('rate_sheet.json', 'r') as file:
        rateSheet = json.load(file)

    number_of_employees = 6  # Example number of employees
    rate_sheet_ee = create_rate_sheet_per_ee(rateSheet, number_of_employees)

    with open('rate_sheet_per_ee.json', 'w') as file:
        file.write(json.dumps(rate_sheet_ee, indent=4))

    print("Rate sheet per employee calculation completed!")
    print("Results saved to 'rate_sheet_per_ee.json'")
    print("\nCalculated rates:")
    print(json.dumps(rate_sheet_ee, indent=2))
