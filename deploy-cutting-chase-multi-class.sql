-- Deployment script for the new multi-class cutting-chase functions
-- Execute this script to create the new functions in your database

-- Step 1: Create the multi-class cutting-chase function
-- This will read from the cutting-chase.sql file
\i multi-class/cutting-chase.sql

-- Step 2: Create the global wrapper function
-- This will read from the cutting-chase-v2.sql file
\i multi-class/cutting-chase-v2.sql

-- Step 3: Verify the functions were created successfully
SELECT 
    routine_name,
    routine_type,
    data_type,
    routine_definition IS NOT NULL as has_definition
FROM information_schema.routines 
WHERE routine_name IN ('fn_get_cutting_chase_multi_class', 'fn_get_cutting_chase_global')
AND routine_schema = 'sandf'
ORDER BY routine_name;

-- Step 4: Grant necessary permissions (if needed)
-- Uncomment if you need to grant permissions to specific roles
/*
GRANT EXECUTE ON FUNCTION sandf.fn_get_cutting_chase_multi_class(TEXT, TEXT) TO your_role_name;
GRANT EXECUTE ON FUNCTION sandf.fn_get_cutting_chase_global(TEXT, TEXT) TO your_role_name;
*/

-- Step 5: Test with a sample plan (replace with your actual plan UUID)
-- Uncomment and modify the following lines to test:
/*
-- Test the global function (recommended)
SELECT jsonb_pretty(
    sandf.fn_get_cutting_chase_global(
        'your-plan-uuid-here',  -- Replace with your actual plan UUID
        'your-user-id'          -- Replace with your user ID (optional)
    )::jsonb
);

-- Test the multi-class function directly
SELECT jsonb_pretty(
    sandf.fn_get_cutting_chase_multi_class(
        'your-plan-uuid-here',  -- Replace with your actual plan UUID
        'your-user-id'          -- Replace with your user ID (optional)
    )::jsonb
);
*/

-- Step 6: Check function signatures
SELECT 
    routine_name,
    parameter_name,
    parameter_mode,
    data_type,
    parameter_default
FROM information_schema.parameters 
WHERE specific_name IN (
    SELECT specific_name 
    FROM information_schema.routines 
    WHERE routine_name IN ('fn_get_cutting_chase_multi_class', 'fn_get_cutting_chase_global')
    AND routine_schema = 'sandf'
)
ORDER BY routine_name, ordinal_position;
